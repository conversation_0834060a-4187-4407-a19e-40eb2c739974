[tool.poetry]
name = "cneutral-doc"
version = "0.1.0"
description = "CNeutral ESG disclosure analytics engine"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"
packages = [{include = "cneutral_doc", from = "src"}]

[tool.poetry.dependencies]
python = ">=3.10,<4.0"
marker-pdf = ">=1.5.2,<2.0.0"
ipykernel = "^6.29.5"
ipywidgets = "^8.1.5"
langchain = "^0.3.20"
langchain-openai = "^0.3.7"
langchain-community = "^0.3.19"
langchain-google-genai = "^2.1.2"
langchain-core = "^0.3.49"
pdfplumber = "^0.11.5"
fastapi = "^0.110.0"
uvicorn = "^0.28.0"
python-dotenv = "^1.0.0"
slowapi = "^0.1.9"
starlette = "^0.36.3"
python-multipart = "^0.0.18"
pre-commit = "^4.2.0"
pandas = "^2.2.3"
aiohttp = ">=3.11.0,!=3.11.14,<4.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^8.3.4"
black = {extras = ["jupyter"], version = "^25.1.0"}
isort = "^6.0.0"
pre-commit = "^4.1.0"
pytest-asyncio = "^0.25.3"
flake8 = "^7.1.2"
nbqa = "^1.9.1"

[tool.isort]
profile = "black"
line_length = 88
known_first_party = ["cneutral_doc"]
treat_comments_as_code = ["# %%"]
use_parentheses = true
include_trailing_comma = true
multi_line_output = 3

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.pytest.ini_options]
asyncio_mode = "strict"
asyncio_default_fixture_loop_scope = "function"
filterwarnings = [
    "ignore::pydantic.PydanticDeprecatedSince20",
    "ignore::DeprecationWarning:pydantic.*:",
    "ignore::pydantic.PydanticDeprecatedSince211",
    "ignore::pytest.PytestDeprecationWarning",
    "ignore::PendingDeprecationWarning:starlette.formparsers:",
]
