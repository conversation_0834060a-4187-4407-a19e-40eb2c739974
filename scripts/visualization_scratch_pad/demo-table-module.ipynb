{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import json\n", "import os\n", "from pathlib import Path\n", "\n", "from dotenv import load_dotenv\n", "\n", "from cneutral_doc.table.classifier import GeminiTableClassifier\n", "from cneutral_doc.utils import get_project_root\n", "\n", "%load_ext autoreload\n", "%autoreload 2\n", "\n", "\n", "load_dotenv()\n", "api_key = os.getenv(\"GOOGLE_API_KEY\")\n", "assert api_key"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["| BUILDING                         | FY2019     | FY2020                                                 | FY2021                                        | FY2022    | FY2023     |  |\n", "|----------------------------------|------------|--------------------------------------------------------|-----------------------------------------------|-----------|------------|--|\n", "| Electricity Consumption (kWh)2   |            |                                                        |                                               |           |            |  |\n", "| Guoco Tower                      | 14,688,394 |                                                        | 13,991,557 13,286,543 13,666,6293 12,775,5623 |           |            |  |\n", "| Guoco Midtown Office             | -          | -                                                      | -                                             | -         | 2,743,946  |  |\n", "| 20 <PERSON><PERSON>er <PERSON>                  | 4,023,271  | 3,879,054                                              | 3,552,523                                     | 3,568,773 | 3,628,518  |  |\n", "| Sofitel Singapore City<br>Centre | 5,162,187  | 4,795,747                                              | 4,371,120                                     | 4,861,321 | 5,076,503  |  |\n", "| Guoco Changfeng City             | -          | -                                                      | -                                             | -         | 1,623,6064 |  |\n", "| Natural Gas (kWh)                |            |                                                        |                                               |           |            |  |\n", "| Sofitel Singapore City<br>Centre | -          | -                                                      | -                                             | -         | 352,979    |  |\n", "| Guoco Changfeng City             | -          | -                                                      | -                                             | -         | 656,691    |  |\n", "| Total Energy<br>Consumption      |            | 23,873,852 22,666,358 21,210,186 22,096,723 26,857,805 |                                               |           |            |  |\n", "| Water Consumption (m3<br>)       |            |                                                        |                                               |           |            |  |\n", "| Guoco Tower                      | 165,425    | 138,888                                                | 94,837                                        | 100,404   | 113,308    |  |\n", "| Guoco Midtown Office             | -          | -                                                      | -                                             | -         | 26,234     |  |\n", "| 20 <PERSON><PERSON><PERSON>                  | 33,215     | 36,160                                                 | 25,692                                        | 21,1455   | 24,6855    |  |\n", "| Sofitel Singapore City<br>Centre | 72,305     | 80,857                                                 | 33,903                                        | 43,168    | 51,427     |  |\n", "| Guoco Changfeng City             | -          | -                                                      | -                                             | -         | 10,166     |  |\n", "| Total Water<br>Consumption       | 270,945    | 255,905                                                | 154,432                                       | 164,717   | 225,820    |  |\n"]}], "source": ["# parsed_doc = f\"{get_project_root()}/tests/data/parsed_doc/apple.json\"\n", "# assert Path(parsed_doc).exists()\n", "# # Load the parsed document\n", "# with open(parsed_doc, \"r\") as f:\n", "#     doc_data = json.load(f)\n", "\n", "\n", "# Read in the table from the path provided\n", "example_table_path = f\"{get_project_root()}/tests/data/visualization/e_table.txt\"\n", "assert Path(example_table_path).exists()\n", "with open(example_table_path, \"r\") as f:\n", "    example_table = f.read()\n", "print(example_table)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["classifier = GeminiTableClassifier(\n", "    model_name=\"gemini-2.0-flash\", temp=0.1, api_key=api_key\n", ")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Classifying table...\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Classification Results:\n", "Environmental Score: 0.90\n", "Social Score: 0.10\n", "Governance Score: 0.00\n", "Other Score: 0.00\n", "Is Valid Table: True\n", "Rows: 18\n", "Columns: 8\n", "Numerical Density: 0.51\n", "\n", "Reasoning:\n", "The table contains data on electricity, natural gas, and water consumption for various buildings, which falls under the Environmental category. There is a small social component due to the mention of workplace locations, but the primary focus is environmental impact. No governance or other factors are present in the table.\n", "\n", "Result as dictionary:\n", "{\n", "  \"e_score\": 0.9,\n", "  \"s_score\": 0.1,\n", "  \"g_score\": 0.0,\n", "  \"other_score\": 0.0,\n", "  \"numerical_density\": 0.51,\n", "  \"rows\": 18,\n", "  \"cols\": 8,\n", "  \"reasoning\": \"The table contains data on electricity, natural gas, and water consumption for various buildings, which falls under the Environmental category. There is a small social component due to the mention of workplace locations, but the primary focus is environmental impact. No governance or other factors are present in the table.\",\n", "  \"validation_message\": null\n", "}\n"]}], "source": ["print(\"Classifying table...\")\n", "result = classifier.classify_table(example_table)\n", "\n", "# Display the results\n", "print(\"\\nClassification Results:\")\n", "print(f\"Environmental Score: {result.e_score:.2f}\")\n", "print(f\"Social Score: {result.s_score:.2f}\")\n", "print(f\"Governance Score: {result.g_score:.2f}\")\n", "print(f\"Other Score: {result.other_score:.2f}\")\n", "print(f\"Is Valid Table: {result.is_valid_table}\")\n", "print(f\"Rows: {result.rows}\")\n", "print(f\"Columns: {result.cols}\")\n", "print(f\"Numerical Density: {result.numerical_density:.2f}\")\n", "\n", "if result.reasoning:\n", "    print(\"\\nReasoning:\")\n", "    print(result.reasoning)\n", "\n", "if result.validation_message:\n", "    print(\"\\nValidation Message:\")\n", "    print(result.validation_message)\n", "\n", "# Convert to dictionary format\n", "print(\"\\nResult as dictionary:\")\n", "result_dict = result.to_dict()\n", "print(json.dumps(result_dict, indent=2))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}