# import os
# import sys
# import json
# import django
# import psycopg2
# from cneutral_doc.table.filter import TableFilter
# from cneutral_doc.document.parser.marker import MarkerParser
# from cneutral_doc.table.visualizer import GeminiTableVisualizer

# from dotenv import load_dotenv
# from cneutral_doc.utils import get_project_root

# load_dotenv()
# PROJECT_ROOT = get_project_root()
# SAVE_ROOT = os.path.join(PROJECT_ROOT, "tests", "data", "visualization")

# # current_dir = os.path.dirname(os.path.abspath(__file__))
# # parent_dir = os.path.dirname(current_dir)
# # print(parent_dir)
# # sys.path.append(parent_dir)

# # os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')
# # django.setup()

# # from api.models import TaxValuableTable, TaxDoc

# # define path of pdf
# pdf_path = os.path.join(PROJECT_ROOT, "cneutral_doc", "pdfs", "773450_2023_Sustainability Report.pdf")


# # parse the pdf
# print("Parsing the PDF...")
# parser = MarkerParser()
# parsed_document = parser.parse(pdf_path, isolate_tables=True)

# # save the parsed document (optional)
# print("Saving the parsed document...")
# with open(f'{SAVE_ROOT}/773450_2023_Sustainability Report_parsed_document.json', 'w') as f:
#     json.dump(parsed_document.to_dict(), f)

# # # load the parsed document if previously saved (optional)
# # with open(f'{SAVE_ROOT}/773450_2023_Sustainability Report_parsed_document.json', 'r') as f:
# #     parsed_document = MarkerParser.from_dict(json.load(f))

# # filter the tables
# print("Filtering the tables...")
# table_filter = TableFilter(api_key=os.getenv("GOOGLE_API_KEY_2"))
# all_tables = table_filter.filter_tables(parsed_document, k=1, save_all=True)

# # get the E, S, G tables safely
# print("Getting the E, S, G tables...")
# e_table = all_tables['E'][0]['content'] if all_tables['E'] else "No environmental tables found"
# s_table = all_tables['S'][0]['content'] if all_tables['S'] else "No social tables found"
# g_table = all_tables['G'][0]['content'] if all_tables['G'] else "No governance tables found"

# # save the tables to files (optional)
# print("Saving the tables to files...")
# with open(f'{SAVE_ROOT}/e_table.txt', 'w') as f:
#     f.write(e_table)
# with open(f'{SAVE_ROOT}/s_table.txt', 'w') as f:
#     f.write(s_table)
# with open(f'{SAVE_ROOT}/g_table.txt', 'w') as f:
#     f.write(g_table)

# # # load the tables from files (optional)
# # with open(f'{SAVE_ROOT}/e_table.txt', 'r') as f:
# #     e_table = f.read()
# # with open(f'{SAVE_ROOT}/s_table.txt', 'r') as f:
# #     s_table = f.read()
# # with open(f'{SAVE_ROOT}/g_table.txt', 'r') as f:
# #     g_table = f.read()

# # generate the code for visualizing the tables
# print("Generating the code for visualizing the tables...")
# visualizer = GeminiTableVisualizer(api_key=os.getenv("GOOGLE_API_KEY_2"))
# e_visualized = visualizer.visualize_table(e_table, 'E') if e_table != "No environmental tables found" else ""
# s_visualized = visualizer.visualize_table(s_table, 'S') if s_table != "No social tables found" else ""
# g_visualized = visualizer.visualize_table(g_table, 'G') if g_table != "No governance tables found" else ""

# # save the code for visualizing the tables to files (optional)
# print("Saving the code for visualizing the tables to files...")
# with open(f'{SAVE_ROOT}/e_visualized.jsx', 'w') as f:
#     f.write(e_visualized)
# with open(f'{SAVE_ROOT}/s_visualized.jsx', 'w') as f:
#     f.write(s_visualized)
# with open(f'{SAVE_ROOT}/g_visualized.jsx', 'w') as f:
#     f.write(g_visualized)

# # # load the code for visualizing the tables from files (optional)
# # with open(f'{SAVE_ROOT}/e_visualized.jsx', 'r') as f:
# #     e_visualized = f.read()
# # with open(f'{SAVE_ROOT}/s_visualized.jsx', 'r') as f:
# #     s_visualized = f.read()
# # with open(f'{SAVE_ROOT}/g_visualized.jsx', 'r') as f:
# #     g_visualized = f.read()

# # add to database
# print("Adding the data to the database...")
# doc_hash = "8e6e12d77a17f4c32d6b1b3c21b348b86cc17c29f32f99edf32c198f5b2bd104"
# e_id = 1
# s_id = 2
# g_id = 3


# def get_doc_by_hash(conn, doc_hash):
#     """Retrieve document by hash."""
#     with conn.cursor() as cur:
#         cur.execute("SELECT doc_hash FROM tax_doc WHERE doc_hash = %s", (doc_hash,))
#         result = cur.fetchone()
#         return result[0] if result else None

# def insert_or_update_table(conn, doc_id, level_1_id, code_str, markdown):
#     """Insert or update a record in the tax_valuable_table."""
#     try:
#         with conn.cursor() as cur:
#             # try to update existing record
#             cur.execute("""
#                 UPDATE tax_valuable_table
#                 SET code_str = %s, markdown = %s
#                 WHERE doc_hash = %s AND level_1_id = %s
#             """, (code_str, markdown, doc_id, level_1_id))

#             # if no rows were updated, insert a new record
#             if cur.rowcount == 0:
#                 cur.execute("""
#                     INSERT INTO tax_valuable_table
#                     (doc_hash, level_1_id, summary, code_str, markdown)
#                     VALUES (%s, %s, %s, %s, %s)
#                 """, (doc_id, level_1_id, '', code_str, markdown))
#                 print(f"Created new entry for doc_hash={doc_hash}, level_1_id={level_1_id}")
#             else:
#                 print(f"Updated entry for doc_hash={doc_hash}, level_1_id={level_1_id}")

#         conn.commit()
#     except psycopg2.Error as e:
#         conn.rollback()
#         print(f"Error adding/updating entry: {e}")

# try:
#     # connect to database
#     conn = psycopg2.connect(
#         dbname='devdb',
#         user='nair6468',
#         password=os.getenv('DEVDB_PASSWORD'),
#         host='localhost',
#         port=5432
#     )

#     # get the document ID
#     doc_id = get_doc_by_hash(conn, doc_hash)

#     if doc_id is None:
#         print(f"No document found with hash {doc_hash}")

#     # prepare data to insert/update
#     data = [
#         {"level_1_id": e_id, "code_str": e_visualized, "markdown": e_table},
#         {"level_1_id": s_id, "code_str": s_visualized, "markdown": s_table},
#         {"level_1_id": g_id, "code_str": g_visualized, "markdown": g_table}
#     ]

#     # insert or update each record
#     for d in data:
#         insert_or_update_table(
#             conn,
#             doc_hash,
#             d["level_1_id"],
#             d["code_str"],
#             d["markdown"]
#         )

#     print("All data inserted/updated successfully")

# except psycopg2.Error as e:
#     print(f"Database connection error: {e}")

# finally:
#     # close the connection if it exists
#     if 'conn' in locals() and conn:
#         conn.close()
