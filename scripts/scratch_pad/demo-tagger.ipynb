{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from cneutral_doc.document.tagger import OpenAIDocTagger\n", "\n", "tagger = OpenAIDocTagger()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'company_name': None,\n", " 'document_title': None,\n", " 'language': None,\n", " 'publication_year': None,\n", " 'doc_type': 'other'}"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["tagger.tag_document(\n", "    \"/home/<USER>/projects/cneutral/doc-intelligence-engine/tests/data/pdfs/mock_pdf.pdf\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}