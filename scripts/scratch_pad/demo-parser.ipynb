{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from cneutral_doc.api.parser.client import CNeutralParserClient\n", "from cneutral_doc.utils import get_project_root\n", "\n", "%load_ext autoreload\n", "%autoreload 2"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "1\n"]}], "source": ["client = CNeutralParserClient()\n", "\n", "small_pdf = f\"{get_project_root()}/tests/data/pdfs/mock_pdf.pdf\"\n", "large_pdf = (\n", "    f\"{get_project_root()}/tests/data/pdfs/Apple_Environmental_Progress_Report_2024.pdf\"\n", ")\n", "\n", "try:\n", "    result = client.parse_document(file_path=small_pdf, isolate_tables=True)\n", "    if isinstance(result, dict):\n", "        print(len(result[\"pages\"]))\n", "        print(len(result[\"tables\"]))\n", "except Exception as e:\n", "    print(f\"Error parsing document: {str(e)}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'content': '| Health Insurance |                   |               |\\n|------------------|-------------------|---------------|\\n| PPO              | Office Copay      | \\\\$10.00       |\\n|                  |                   | \\\\$1500 Single |\\n|                  | Out- of -pocket   | \\\\$2750 Family |\\n|                  | Maximum           |               |\\n|                  |                   |               |\\n|                  | Family            | \\\\$350 monthly |\\n| HMO              |                   |               |\\n|                  | Office Copay      | \\\\$5.00 Single |\\n|                  |                   | \\\\$1000 Single |\\n|                  | Out - of - pocket | \\\\$2250 Family |\\n|                  |                   |               |\\n|                  | Family            | \\\\$250 Monthly |',\n", " 'index': 0,\n", " 'page_number': 1}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["result[\"tables\"][0]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}