import json
import logging
import os
from pathlib import Path

from dotenv import load_dotenv

from cneutral_doc.document.parser.marker import MarkerParser
from cneutral_doc.table.filter import TableFilter
from cneutral_doc.table.visualizer import GeminiTableVisualizer
from cneutral_doc.utils import get_project_root, setup_logging

# Load environment variables and set up logging
load_dotenv()
setup_logging()

logger = logging.getLogger(__name__)
PROJECT_ROOT = Path(get_project_root())

# define path of pdf
pdf_path = PROJECT_ROOT / "tests" / "data" / "pdfs" / "annual-review-2024-en.pdf"

logger.info(f"Starting filtering pipeline for: {pdf_path}")

# parse the pdf
logger.info("Parsing the PDF...")
parser = MarkerParser()
parsed_document = parser.parse(str(pdf_path), isolate_tables=True)

# save the parsed document (optional)
logger.info("Saving the parsed document...")
output_path = (
    PROJECT_ROOT / "tests" / "data" / "filtering" / "annual-review-2024-en.json"
)
with open(output_path, "w") as f:
    json.dump(parsed_document.to_dict(), f)

# # load the parsed document if previously saved (optional)
# with open(output_path, 'r') as f:
#     parsed_document = MarkerParser.from_dict(json.load(f))

# filter the tables
logger.info("Filtering the tables...")
api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GOOGLE_API_KEY_2")
if not api_key:
    raise ValueError(
        "No Google API key found. Set GOOGLE_API_KEY environment variable."
    )

table_filter = TableFilter(api_key=api_key)
all_tables = table_filter.filter_tables(parsed_document, k=1)

# save the filtered tables (optional)
logger.info("Saving the filtered tables...")
filtered_output_path = (
    PROJECT_ROOT
    / "tests"
    / "data"
    / "filtering"
    / "annual-review-2024-en_filtered_tables.json"
)
with open(filtered_output_path, "w") as f:
    json.dump(all_tables, f, indent=2, default=str)

# get the E, S, G tables safely
logger.info("Getting the E, S, G tables...")
e_table = (
    all_tables["E"][0]["content"]
    if all_tables["E"]
    else "No environmental tables found"
)
s_table = all_tables["S"][0]["content"] if all_tables["S"] else "No social tables found"
g_table = (
    all_tables["G"][0]["content"] if all_tables["G"] else "No governance tables found"
)

# Log results
logger.info(f"Found {len(all_tables['E'])} environmental tables")
logger.info(f"Found {len(all_tables['S'])} social tables")
logger.info(f"Found {len(all_tables['G'])} governance tables")

# save the tables to files (optional)
logger.info("Saving the tables to files...")
viz_dir = PROJECT_ROOT / "tests" / "data" / "visualization"
viz_dir.mkdir(exist_ok=True)

with open(viz_dir / "annual-review-2024-en_e_table.txt", "w") as f:
    f.write(e_table)
with open(viz_dir / "annual-review-2024-en_s_table.txt", "w") as f:
    f.write(s_table)
with open(viz_dir / "annual-review-2024-en_g_table.txt", "w") as f:
    f.write(g_table)

logger.info("Filtering pipeline completed successfully!")

# # load the tables from files (optional)
# with open(viz_dir / "annual-review-2024-en_e_table.txt", 'r') as f:
#     e_table = f.read()
# with open(viz_dir / "annual-review-2024-en_s_table.txt", 'r') as f:
#     s_table = f.read()
# with open(viz_dir / "annual-review-2024-en_g_table.txt", 'r') as f:
#     g_table = f.read()
