import json
import os
import time

from dotenv import load_dotenv

from cneutral_doc.utils import get_project_root

load_dotenv()

PROJECT_ROOT = get_project_root()

true_json_path = f"{PROJECT_ROOT}/tests/data/parsed_doc/apple_auto_isolate_true.json"
false_json_path = f"{PROJECT_ROOT}/tests/data/parsed_doc/apple_auto_isolate_false.json"

with open(true_json_path, "r") as f:
    true_json = json.load(f)

with open(false_json_path, "r") as f:
    false_json = json.load(f)

mismatch_count = 0
for table_1, table_2 in zip(true_json["tables"], false_json["tables"]):
    if table_1["content"] != table_2["content"]:
        mismatch_count += 1
        print(table_1["content"])
        print("=" * 100)
        print(table_2["content"])

print(f"Mismatch count: {mismatch_count}")
