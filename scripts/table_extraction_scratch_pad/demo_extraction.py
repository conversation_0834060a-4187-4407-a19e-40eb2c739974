import json
import os
import time

from dotenv import load_dotenv

from cneutral_doc.document.parser.marker import MarkerParser
from cneutral_doc.utils import get_project_root

load_dotenv()

PROJECT_ROOT = get_project_root()

# define path of pdf
# pdf_path = "/nfs/newshare/users/nairm/doc-intelligence/backend/cneutral_doc/pdfs/773450_2023_Sustainability Report.pdf"
pdf_path = (
    f"{PROJECT_ROOT}/tests/data/pdfs/Apple_Environmental_Progress_Report_2024.pdf"
)
# pdf_path = "/nfs/newshare/users/nairm/doc-intelligence-engine/tests/data/pdfs/mock_pdf.pdf"

# parse the pdf
print("Parsing the PDF...")
start_time = time.time()
parser = MarkerParser()
# set auto_isolate to True to use marker parser to isolate tables, False to use manual isolation (faster)
auto_isolate = False
parsed_document = parser.parse(pdf_path, isolate_tables=True, auto_isolate=auto_isolate)
end_time = time.time()
parsing_time = end_time - start_time
print(f"PDF parsing completed in {parsing_time:.2f} seconds")

# save the parsed document (optional)
print("Saving the parsed document...")
with open(
    f"{PROJECT_ROOT}/tests/data/parsed_doc/apple_auto_isolate_{str(auto_isolate).lower()}.json",
    "w",
) as f:
    json.dump(parsed_document.to_dict(), f)

# # load the parsed document if previously saved (optional)
# with open('parsed_document.json', 'r') as f:
#     parsed_document = MarkerParser.from_dict(json.load(f))
