#!/bin/bash

# Script to uninstall the CNeutral Parser Service
# This script must be run with sudo privileges

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
  echo "Please run this script with sudo privileges"
  exit 1
fi

# Stop the service
systemctl stop cneutral-parser.service

# Disable the service
systemctl disable cneutral-parser.service

# Remove the service file
rm -f /etc/systemd/system/cneutral-parser.service

# Reload systemd
systemctl daemon-reload

echo ""
echo "CNeutral Parser Service has been uninstalled."
echo ""
