#!/bin/bash

# Script to check the status of the CNeutral Parser Service and view its logs
# Usage: ./check_parser_service.sh [--logs N] [--test-api]
#   --logs N : Show N lines of logs (default: 20)
#   --test-api : Test if the API is responding

# Parse command line arguments
LOG_LINES=20
TEST_API=false

while [[ $# -gt 0 ]]; do
  case $1 in
    --logs)
      LOG_LINES="$2"
      shift 2
      ;;
    --test-api)
      TEST_API=true
      shift
      ;;
    *)
      echo "Unknown option: $1"
      echo "Usage: ./check_parser_service.sh [--logs N] [--test-api]"
      exit 1
      ;;
  esac
done

# Check if service exists
if ! systemctl list-unit-files | grep -q cneutral-parser.service; then
  echo "CNeutral Parser Service is not installed!"
  echo "Run 'sudo ./install_parser_service.sh' to install it."
  exit 1
fi

# Get the script directory and project root directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Check service status
echo "=== Service Status ==="
sudo systemctl status cneutral-parser.service || true
echo ""

# Check if service is active
if systemctl is-active --quiet cneutral-parser.service; then
  echo "Service is ACTIVE"
  
  # Get service uptime
  STARTED_AT=$(systemctl show -p ActiveEnterTimestamp cneutral-parser.service | sed 's/ActiveEnterTimestamp=//g')
  echo "Started at: $STARTED_AT"
  
  # Get memory usage
  echo "\nMemory Usage:"
  sudo systemctl show cneutral-parser.service -p MemoryCurrent | sed 's/MemoryCurrent=//g' | awk '{printf "%.2f MB\n", $1/1024/1024}'
  
  # Get CPU usage
  echo "\nCPU Usage:"
  PID=$(systemctl show -p MainPID cneutral-parser.service | sed 's/MainPID=//g')
  if [ "$PID" != "0" ]; then
    ps -p $PID -o %cpu,%mem | tail -n 1
  else
    echo "Service not running (no PID)"
  fi
else
  echo "Service is NOT ACTIVE"
  echo "Run 'sudo systemctl start cneutral-parser.service' to start it"
fi

# Check if service is enabled
if systemctl is-enabled --quiet cneutral-parser.service; then
  echo "\nService is ENABLED (will start on boot)"
else
  echo "\nService is NOT ENABLED (will not start on boot)"
  echo "Run 'sudo systemctl enable cneutral-parser.service' to enable it"
fi

# Test API if requested
if [ "$TEST_API" = true ]; then
  echo "\n=== API Connectivity Test ==="
  # Check if curl is installed
  if ! command -v curl &> /dev/null; then
    echo "curl not found. Please install curl to test API connectivity."
  else
    echo "Testing API connection at http://localhost:8001..."
    HTTP_CODE=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8001)
    
    if [ "$HTTP_CODE" = "404" ] || [ "$HTTP_CODE" = "200" ] || [ "$HTTP_CODE" = "401" ] || [ "$HTTP_CODE" = "403" ]; then
      echo "API is responding (HTTP $HTTP_CODE)"
      echo "API documentation available at: http://localhost:8001/docs"
    else
      echo "API is not responding properly (HTTP $HTTP_CODE)"
      echo "Check service logs for more information"
    fi
  fi
fi

echo "\n=== Recent Logs ($LOG_LINES lines) ==="
sudo journalctl -u cneutral-parser.service -n $LOG_LINES --no-pager

echo ""
echo "=== Useful Commands ==="
echo "View live logs:              sudo journalctl -u cneutral-parser.service -f"
echo "Restart service:             sudo systemctl restart cneutral-parser.service"
echo "Stop service:                sudo systemctl stop cneutral-parser.service"
echo "View API documentation:      http://localhost:8001/docs"
echo "Test API with sample PDF:    poetry run python -m cneutral_doc.api.parser.client --input <pdf-file> --output results.json"
echo ""
