#!/bin/bash

# Script to restart the CNeutral Parser Service
# This script can restart either the systemd service or run a local instance with auto-restart
# Usage: 
#   With systemd: sudo ./restart_parser_service.sh --systemd
#   Local instance: ./restart_parser_service.sh

# Get the script directory and project root directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

# Log file for the service
LOG_FILE="$PROJECT_ROOT/logs/parser_service_auto_restart.log"

# Check if logs directory exists
if [ ! -d "$PROJECT_ROOT/logs" ]; then
  mkdir -p "$PROJECT_ROOT/logs"
  echo "Created logs directory at $PROJECT_ROOT/logs"
fi

# Function to restart the systemd service
restart_systemd_service() {
  # Check if running with sudo
  if [ "$EUID" -ne 0 ]; then
    echo "Please run with sudo when using --systemd option"
    exit 1
  fi

  # Check if service exists
  if ! systemctl list-unit-files | grep -q cneutral-parser.service; then
    echo "CNeutral Parser Service is not installed!"
    echo "Run 'sudo ./install_parser_service.sh' to install it first."
    exit 1
  fi
  
  # Check if service is already running
  if systemctl is-active --quiet cneutral-parser.service; then
    echo "CNeutral Parser Service is currently running, stopping it first..."
    systemctl stop cneutral-parser.service
    sleep 2
  fi

  echo "Starting CNeutral Parser Service via systemd..."
  systemctl start cneutral-parser.service
  
  # Check if restart was successful
  if systemctl is-active --quiet cneutral-parser.service; then
    echo "Service successfully started and is now ACTIVE"
    echo "View logs with: sudo journalctl -u cneutral-parser.service -f"
    echo "Log file location: $PROJECT_ROOT/logs/api_server.log"
    echo "Service status:"
    systemctl status cneutral-parser.service
  else
    echo "Failed to start service! Service is NOT ACTIVE"
    echo "Check logs with: sudo journalctl -u cneutral-parser.service -e"
    echo "Or check logs at: $PROJECT_ROOT/logs/api_server.log"
    exit 1
  fi
}

# Function to run local instance with auto-restart
run_local_instance() {
  # Change to the project root directory
  cd "$PROJECT_ROOT" || { echo "Failed to change to project directory"; exit 1; }

  echo "Starting parser service with automatic restart..."
  echo "Press Ctrl+C to exit"
  echo "Logs will be written to $LOG_FILE"

  # Set up trap to handle script termination
  trap 'echo "Exiting..."; exit 0' SIGINT SIGTERM

  # Counter for restarts
  RESTART_COUNT=0

  # Check if Poetry is installed and find the correct path when running with sudo
  POETRY_CMD=""
  
  # Try standard command first
  if command -v poetry &> /dev/null; then
    POETRY_CMD="poetry"
  # Try common installation locations
  elif [ -x "$HOME/.poetry/bin/poetry" ]; then
    POETRY_CMD="$HOME/.poetry/bin/poetry"
  elif [ -x "$HOME/.local/bin/poetry" ]; then
    POETRY_CMD="$HOME/.local/bin/poetry"
  else
    echo "Poetry not found! Please install Poetry or run with --systemd option."
    echo "If you have Poetry installed, you can run this script without sudo."
    exit 1
  fi
  
  echo "Using Poetry at: $POETRY_CMD"

  while true; do
    # Increment restart counter
    RESTART_COUNT=$((RESTART_COUNT + 1))
    
    # Log restart information
    echo "$(date) - Starting parser service (Restart #$RESTART_COUNT)" | tee -a "$LOG_FILE"
    
    # Run the service
    echo "Running parser service from: $PROJECT_ROOT"
    echo "Logs will be written to: $PROJECT_ROOT/logs/api_server.log"
    $POETRY_CMD run python -m cneutral_doc.api.parser.service
    
    # If we get here, the service has terminated
    EXIT_CODE=$?
    
    echo "$(date) - Parser service terminated with exit code $EXIT_CODE (Restart #$RESTART_COUNT)" | tee -a "$LOG_FILE"
    
    # Wait a bit before restarting to avoid rapid restart loops
    echo "Waiting 5 seconds before restarting..."
    sleep 5
  done
}

# Parse command line arguments
if [ "$1" == "--systemd" ]; then
  restart_systemd_service
else
  # If running with sudo without the --systemd flag, warn the user
  if [ "$EUID" -eq 0 ]; then
    echo "Warning: Running in local mode with sudo may cause issues with Poetry."
    echo "Consider running without sudo or use the --systemd flag with sudo."
    echo "Continuing anyway..."
  fi
  run_local_instance
fi
