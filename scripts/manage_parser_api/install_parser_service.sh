#!/bin/bash

# Script to install the CNeutral Parser Service as a systemd service
# This script must be run with sudo privileges

# Check if running with sudo
if [ "$EUID" -ne 0 ]; then
  echo "Please run this script with sudo privileges"
  exit 1
fi

# Get the current user who invoked sudo
CURRENT_USER=$(logname || echo $SUDO_USER)
if [ -z "$CURRENT_USER" ]; then
  echo "Could not determine the current user"
  exit 1
fi

# Get the script directory and project root directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
cd "$SCRIPT_DIR"

# Verify project structure
if [ ! -d "$PROJECT_ROOT/src/cneutral_doc" ]; then
  echo "Error: Could not find the project source directory at $PROJECT_ROOT/src/cneutral_doc"
  echo "Make sure you're running this script from the correct location"
  exit 1
fi

# Check if service module exists
if [ ! -f "$PROJECT_ROOT/src/cneutral_doc/api/parser/service.py" ]; then
  echo "Error: Could not find the service.py file at $PROJECT_ROOT/src/cneutral_doc/api/parser/service.py"
  exit 1
fi

# Find the poetry executable path
POETRY_PATH=""
if sudo -u $CURRENT_USER which poetry > /dev/null 2>&1; then
  POETRY_PATH=$(sudo -u $CURRENT_USER which poetry)
  echo "Found Poetry at: $POETRY_PATH"
else
  # Try common locations
  if [ -f "/home/<USER>/.local/bin/poetry" ]; then
    POETRY_PATH="/home/<USER>/.local/bin/poetry"
    echo "Found Poetry at: $POETRY_PATH"
  else
    echo "Warning: Could not find poetry executable. Please make sure poetry is installed."
    echo "You may need to manually edit the service file with the correct path."
    POETRY_PATH="/home/<USER>/.local/bin/poetry"
  fi
fi

echo "Installing CNeutral Parser Service with the following settings:"
echo "User: $CURRENT_USER"
echo "Project Root: $PROJECT_ROOT"
echo "Poetry Path: $POETRY_PATH"
echo ""

# Create a backup of the original service file
cp cneutral-parser.service cneutral-parser.service.bak

# Update the service file with the correct user, paths, and poetry path
sed -i "s|User=xinmatrix|User=$CURRENT_USER|g" cneutral-parser.service
sed -i "s|Group=xinmatrix|Group=$CURRENT_USER|g" cneutral-parser.service
sed -i "s|WorkingDirectory=/home/<USER>/projects/cneutral/doc-intelligence-engine|WorkingDirectory=$PROJECT_ROOT|g" cneutral-parser.service
sed -i "s|/home/<USER>/.local/bin/poetry|$POETRY_PATH|g" cneutral-parser.service
sed -i "s|Environment=\"PATH=/home/<USER>/.local/bin|Environment=\"PATH=/home/<USER>/.local/bin|g" cneutral-parser.service
sed -i "s|Environment=\"PYTHONPATH=/home/<USER>/projects/cneutral/doc-intelligence-engine\"|Environment=\"PYTHONPATH=$PROJECT_ROOT\"|g" cneutral-parser.service

# Copy the service file to the systemd directory
echo "Copying service file to /etc/systemd/system/"
cp cneutral-parser.service /etc/systemd/system/

# Reload systemd to recognize the new service
echo "Reloading systemd daemon"
systemctl daemon-reload

# Enable the service to start on boot
echo "Enabling service to start on boot"
systemctl enable cneutral-parser.service

# Start the service
echo "Starting CNeutral Parser Service"
systemctl start cneutral-parser.service

# Check the status
echo "\nService Status:\n"
systemctl status cneutral-parser.service

echo ""
echo "CNeutral Parser Service has been installed and started."
echo "You can check its status with: sudo systemctl status cneutral-parser.service"
echo "View logs with: sudo journalctl -u cneutral-parser.service -f"
echo "Restart the service with: sudo systemctl restart cneutral-parser.service"
echo ""

# Create logs directory if it doesn't exist
if [ ! -d "$PROJECT_ROOT/logs" ]; then
  echo "Creating logs directory at $PROJECT_ROOT/logs"
  sudo -u $CURRENT_USER mkdir -p "$PROJECT_ROOT/logs"
fi

echo "Installation complete!"

