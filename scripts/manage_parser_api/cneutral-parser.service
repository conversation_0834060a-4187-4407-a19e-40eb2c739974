[Unit]
Description=CNeutral Document Parser API Service
After=network.target
Wants=network.target

[Service]
Type=simple
User=xinmatrix
Group=xinmatrix
WorkingDirectory=/home/<USER>/projects/cneutral/doc-intelligence-engine
ExecStart=/home/<USER>/.local/bin/poetry run python -m cneutral_doc.api.parser.service
Restart=always
RestartSec=10
StartLimitIntervalSec=60
StartLimitBurst=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=cneutral-parser
Environment="PYTHONUNBUFFERED=1"
Environment="PATH=/home/<USER>/.local/bin:/usr/local/bin:/usr/bin:/bin"
Environment="PYTHONPATH=/home/<USER>/projects/cneutral/doc-intelligence-engine"

# Hardening measures
ProtectSystem=full
PrivateTmp=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
