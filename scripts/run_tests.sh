#!/bin/bash
# Script to run tests with or without resource-intensive tests
# This script provides a convenient way to skip slow or GPU-intensive tests
# while maintaining the ability to run the full test suite when needed.

# Default values
SKIP_SLOW=false
SKIP_GPU=false
VERBOSE=false
COVERAGE=false
TEST_PATH="tests/"
PYTEST_ARGS=""

# Print usage information
function print_usage {
  echo "Usage: $0 [OPTIONS]"
  echo "Options:"
  echo "  --skip-slow         Skip tests marked with @pytest.mark.slow"
  echo "  --skip-gpu          Skip tests marked with @pytest.mark.gpu"
  echo "  --verbose, -v       Run tests with verbose output"
  echo "  --coverage, -c      Generate test coverage report"
  echo "  --path PATH         Specify a path to test (default: tests/)"
  echo "  --help, -h          Show this help message"
  echo "  --                  Pass remaining arguments directly to pytest"
  echo ""
  echo "Examples:"
  echo "  $0 --skip-slow --skip-gpu     # Skip both slow and GPU tests"
  echo "  $0 --path tests/test_api      # Run only API tests"
  echo "  $0 -v -c                      # Run all tests with verbose output and coverage"
  echo "  $0 -- -xvs                    # Pass -xvs directly to pytest"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --skip-slow)
      SKIP_SLOW=true
      shift
      ;;
    --skip-gpu)
      SKIP_GPU=true
      shift
      ;;
    --verbose|-v)
      VERBOSE=true
      shift
      ;;
    --coverage|-c)
      COVERAGE=true
      shift
      ;;
    --path)
      if [[ -z "$2" || "$2" == --* ]]; then
        echo "Error: --path requires an argument"
        print_usage
        exit 1
      fi
      TEST_PATH="$2"
      shift 2
      ;;
    --help|-h)
      print_usage
      exit 0
      ;;
    --)
      shift
      PYTEST_ARGS="$*"
      break
      ;;
    *)
      echo "Error: Unknown option: $1"
      print_usage
      exit 1
      ;;
  esac
done

# Set environment variables based on arguments
if [ "$SKIP_SLOW" = true ]; then
  export SKIP_SLOW_TESTS=1
  echo "⏩ Skipping slow tests (marked with @pytest.mark.slow)"
fi

if [ "$SKIP_GPU" = true ]; then
  export SKIP_GPU_TESTS=1
  echo "⏩ Skipping GPU tests (marked with @pytest.mark.gpu)"
fi

# Build the pytest command
PYTEST_CMD="poetry run pytest"

if [ "$VERBOSE" = true ]; then
  PYTEST_CMD="$PYTEST_CMD -v"
fi

if [ "$COVERAGE" = true ]; then
  PYTEST_CMD="$PYTEST_CMD --cov=cneutral_doc --cov-report=term"
  echo "📊 Generating test coverage report"
fi

# Add any additional pytest arguments
if [ -n "$PYTEST_ARGS" ]; then
  PYTEST_CMD="$PYTEST_CMD $PYTEST_ARGS"
fi

# Run the tests
echo "🧪 Running tests in $TEST_PATH"
$PYTEST_CMD "$TEST_PATH"

# Store the exit code
EXIT_CODE=$?

# Clean up environment variables
unset SKIP_SLOW_TESTS
unset SKIP_GPU_TESTS

# Exit with the pytest exit code
exit $EXIT_CODE
