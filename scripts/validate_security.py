#!/usr/bin/env python3
"""
Security validation script for the doc-intelligence-engine project.

This script validates that:
1. No sensitive data is committed to git
2. Environment variables are properly handled
3. API keys are properly mocked in tests
4. Logging is properly configured
"""

import os
import re
import subprocess
import sys
from pathlib import Path
from typing import List, Tuple

from cneutral_doc.utils import setup_logging

# Set up logging for this script
setup_logging()
import logging

logger = logging.getLogger(__name__)


def check_git_history_for_secrets() -> List[str]:
    """Check git history for potential secrets."""
    issues = []

    # Patterns that might indicate secrets
    secret_patterns = [
        r'api_key\s*=\s*["\'][^"\']{20,}["\']',  # API keys
        r'password\s*=\s*["\'][^"\']+["\']',  # Passwords
        r'secret\s*=\s*["\'][^"\']+["\']',  # Secrets
        r'token\s*=\s*["\'][^"\']{20,}["\']',  # Tokens
    ]

    try:
        # Check current files
        for pattern in secret_patterns:
            result = subprocess.run(
                ["grep", "-r", "-E", pattern, "src/", "scripts/", "tests/"],
                capture_output=True,
                text=True,
                cwd=Path.cwd(),
            )
            if result.returncode == 0 and result.stdout:
                # Filter out test files with mock values
                lines = result.stdout.strip().split("\n")
                for line in lines:
                    if "test" not in line.lower() and "mock" not in line.lower():
                        issues.append(f"Potential secret in current files: {line}")

        logger.info("Git history check completed")
    except subprocess.CalledProcessError as e:
        logger.warning(f"Could not check git history: {e}")

    return issues


def check_env_files() -> List[str]:
    """Check for .env files and their git status."""
    issues = []

    # Check if .env files exist
    env_files = list(Path.cwd().glob(".env*"))

    for env_file in env_files:
        if env_file.name == ".env":
            # .env file should exist locally but be gitignored
            logger.info(
                f"✓ Found local .env file: {env_file} (this is correct for local development)"
            )
        elif env_file.name == ".env.example":
            # Check if .env.example has placeholder values
            try:
                content = env_file.read_text()
                if any(
                    suspicious in content
                    for suspicious in ["sk-", "AIza", "gho_", "ghp_"]
                ):
                    issues.append(f"Potential real secrets in {env_file}")
                else:
                    logger.info(f"✓ {env_file} contains only placeholder values")
            except Exception as e:
                issues.append(f"Could not read {env_file}: {e}")

    # Check .gitignore
    gitignore_path = Path.cwd() / ".gitignore"
    if gitignore_path.exists():
        gitignore_content = gitignore_path.read_text()
        if ".env" not in gitignore_content:
            issues.append(".env is not in .gitignore - this is a security risk!")
        else:
            logger.info("✓ .env is properly ignored in .gitignore")

            # Additional check: verify .env is actually ignored by git
            try:
                result = subprocess.run(
                    ["git", "check-ignore", ".env"],
                    capture_output=True,
                    text=True,
                    cwd=Path.cwd(),
                )
                if result.returncode == 0:
                    logger.info("✓ .env file is properly ignored by git")
                else:
                    issues.append(
                        ".env file is not being ignored by git despite being in .gitignore"
                    )
            except subprocess.CalledProcessError:
                logger.warning("Could not verify git ignore status for .env file")
    else:
        issues.append("No .gitignore file found")

    return issues


def check_environment_variable_handling() -> List[str]:
    """Check that environment variables are properly handled."""
    issues = []

    # Check source files for proper environment variable usage
    src_files = list(Path("src").rglob("*.py"))

    for src_file in src_files:
        try:
            content = src_file.read_text()

            # Check for hardcoded API keys
            if re.search(r'api_key\s*=\s*["\'][A-Za-z0-9]{20,}["\']', content):
                issues.append(f"Potential hardcoded API key in {src_file}")

            # Check for proper environment variable usage
            if "os.environ.get(" in content:
                logger.debug(
                    f"✓ {src_file} uses os.environ.get() for environment variables"
                )

        except Exception as e:
            logger.warning(f"Could not read {src_file}: {e}")

    return issues


def check_test_mocking() -> List[str]:
    """Check that tests properly mock external dependencies."""
    issues = []

    test_files = list(Path("tests").rglob("*.py"))

    for test_file in test_files:
        try:
            content = test_file.read_text()

            # Check if tests that use API keys have proper mocking
            if "api_key" in content and "mock" not in content.lower():
                # Look for actual API calls without mocking
                if any(
                    pattern in content for pattern in ["invoke(", "request(", "call("]
                ):
                    if "Mock" not in content and "@patch" not in content:
                        issues.append(f"Potential unmocked API calls in {test_file}")

        except Exception as e:
            logger.warning(f"Could not read {test_file}: {e}")

    return issues


def check_logging_configuration() -> List[str]:
    """Check that logging is properly configured."""
    issues = []

    # Check module files for improper logging configuration
    module_files = list(Path("src").rglob("*.py"))

    for module_file in module_files:
        try:
            content = module_file.read_text()

            # Check for manual logging configuration in modules
            if any(
                pattern in content
                for pattern in [
                    "logging.basicConfig",
                    "FileHandler(",
                    "StreamHandler(",
                    "addHandler(",
                    "setLevel(",
                ]
            ):
                # Allow these in utils.py
                if "utils.py" not in str(module_file):
                    issues.append(
                        f"Manual logging configuration found in module {module_file}"
                    )

            # Check for proper logger creation
            if "logger = logging.getLogger(__name__)" in content:
                logger.debug(f"✓ {module_file} uses proper module-level logger")

        except Exception as e:
            logger.warning(f"Could not read {module_file}: {e}")

    # Check script files for proper logging setup
    script_files = list(Path("scripts").rglob("*.py"))

    for script_file in script_files:
        try:
            content = script_file.read_text()

            if "setup_logging()" in content:
                logger.debug(f"✓ {script_file} uses setup_logging()")
            elif "logging" in content:
                issues.append(
                    f"Script {script_file} uses logging but not setup_logging()"
                )

        except Exception as e:
            logger.warning(f"Could not read {script_file}: {e}")

    return issues


def main():
    """Run all security validation checks."""
    logger.info("Starting security validation...")

    all_issues = []

    # Run all checks
    checks = [
        ("Git history secrets", check_git_history_for_secrets),
        ("Environment files", check_env_files),
        ("Environment variable handling", check_environment_variable_handling),
        ("Test mocking", check_test_mocking),
        ("Logging configuration", check_logging_configuration),
    ]

    for check_name, check_func in checks:
        logger.info(f"Running {check_name} check...")
        issues = check_func()
        if issues:
            logger.warning(f"{check_name} check found {len(issues)} issues")
            all_issues.extend(issues)
        else:
            logger.info(f"✓ {check_name} check passed")

    # Report results
    if all_issues:
        logger.error(f"Security validation failed with {len(all_issues)} issues:")
        for issue in all_issues:
            logger.error(f"  - {issue}")
        sys.exit(1)
    else:
        logger.info("✅ All security validation checks passed!")
        sys.exit(0)


if __name__ == "__main__":
    main()
