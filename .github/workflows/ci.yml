name: Test and Code Quality

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    
    - name: Install Poetry
      run: |
        curl -sSL https://install.python-poetry.org | python3 -
    
    - name: Install dependencies
      run: |
        poetry install
    
    # - name: Run tests
    #   run: |
    #     poetry run pytest
    
    - name: Check code formatting (Black)
      run: |
        poetry run black --check .
    
    - name: Check imports (isort)
      run: |
        poetry run isort --check-only .
