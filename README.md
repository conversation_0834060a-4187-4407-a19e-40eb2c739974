# CNeutral ESG Disclosure Analytics Engine

Welcome to the CNeutral ESG Disclosure Analytics Engine, a specialized tool for processing and analyzing PDF documents related to Environmental, Social, and Governance (ESG) disclosures. This engine provides comprehensive functionality for parsing documents, extracting metadata, classifying content, and visualizing data related to sustainability and corporate responsibility.

## Overview

CNeutral is designed to assist ESG analysts by automating the extraction and analysis of key information from corporate reports. The engine can process various document types including annual reports, sustainability reports, and integrated reports to provide structured insights.

## Key Features

- **Document Parsing**: Convert PDF documents into structured formats with extracted text, tables, and images using the Marker library.
- **Metadata Extraction**: Automatically tag documents with relevant metadata including company name, document title, publication year, and document type using LLM-based taggers.
- **Table Analysis**: Identify, classify, and visualize tables within documents with specialized classifiers and filters.
- **ESG Insights** (Planned): Analyze document content against a comprehensive set of ESG factors to extract relevant quotes and decisions using Google's Gemini AI model.

## Project Structure

- `src/cneutral_doc/`: Core library code
  - `api/`: API services for document parsing and analysis
  - `analyzer/`: ESG factor analysis using Gemini AI (planned)
  - `document/`: Document parsing and tagging functionality
  - `table/`: Table classification and visualization
- `scripts/`: Utility scripts for processing and testing
- `tests/`: Unit and integration tests

## Getting Started

1. **Installation**: Clone this repository and install dependencies using Poetry:
   ```bash
   poetry install
   ```

2. **Configuration**: Set up your environment variables in a `.env` file. Create a `.env` file in the project root with the following variables:
   ```
   # API Keys for AI services
   GOOGLE_API_KEY=your_google_api_key_here
   OPENAI_API_KEY=your_openai_api_key_here

   # API Key for the CNeutral API service
   CNEUTRAL_API_KEY=your_cneutral_api_key_here
   ```

3. **Usage**: Run the pipeline scripts or integrate the library into your ESG analysis workflow:
   ```bash
   # Example: Run the parser API service
   python -m cneutral_doc.api.parser.service
   ```

## Main Classes and Usage

The CNeutral ESG Disclosure Analytics Engine is built around several key classes that handle different aspects of document processing and analysis:

### Document Parsing

- **`MarkerParser`** (`cneutral_doc.document.parser.marker`):
  - **Purpose**: Implements PDF parsing using the Marker library to extract text, tables, and images.
  - **Key Methods**:
    - `parse(pdf_path, isolate_tables=False, auto_isolate=False)`: Parses a PDF into a structured `ParsedDocument` object. Use `isolate_tables=True` to extract tables separately.
  - **Usage**:
    ```python
    from cneutral_doc.document.parser.marker import MarkerParser
    parser = MarkerParser()
    parsed_doc = parser.parse("path/to/report.pdf", isolate_tables=True)
    ```

- **`Table`** (`cneutral_doc.document.parser.base`):
  - **Purpose**: Dataclass representing a parsed table from a document.
  - **Key Attributes**:
    - `content`: The textual content of the table.
    - `index`: The sequential index of the table in the document.
    - `page_number`: The page number where the table appears in the document.

### Document Tagging

- **`OpenAIDocTagger`** (`cneutral_doc.document.tagger`):
  - **Purpose**: Uses OpenAI models to extract metadata and classify document types (annual report, ESG report, etc.).
  - **Key Methods**:
    - `tag_document(document_path)`: Returns a dictionary of extracted metadata like company name, title, and document type.
  - **Usage**:
    ```python
    from cneutral_doc.document.tagger import OpenAIDocTagger
    tagger = OpenAIDocTagger()
    metadata = tagger.tag_document("path/to/report.pdf")
    print(metadata)
    ```

### Table Processing

- **`TableFilter`** (`cneutral_doc.table.filter`):
  - **Purpose**: Filters and processes extracted tables based on specific criteria.
  - **Key Methods**:
    - `filter_tables(document, save_path=None, k=5)`: Filters tables from a parsed document, optionally saving results to a specified path.
  - **Usage**:
    ```python
    from cneutral_doc.table.filter import TableFilter

    # API key is required for the classifier
    api_key = os.environ.get("GOOGLE_API_KEY")
    filter = TableFilter(api_key=api_key)
    filtered_tables = filter.filter_tables(parsed_doc, save_path="results.json")
    ```

- **`GeminiTableClassifier`** (`cneutral_doc.table.classifier`):
  - **Purpose**: Classifies tables using Google's Gemini AI model.
  - **Usage**:
    ```python
    from cneutral_doc.table.classifier import GeminiTableClassifier

    # API key is required for the classifier
    api_key = os.environ.get("GOOGLE_API_KEY")
    classifier = GeminiTableClassifier(api_key=api_key)
    result = classifier.classify_table(table.content)
    ```

### API Services

- **`CNeutralParserClient`** (`cneutral_doc.api.parser.client`):
  - **Purpose**: Client for interacting with the CNeutral document parser API.
  - **Key Methods**:
    - `parse_document(file_path, isolate_tables=False, format="markdown")`: Sends a document to the parser service and returns the parsed result.
  - **Usage**:
    ```python
    from cneutral_doc.api.parser.client import CNeutralParserClient

    client = CNeutralParserClient(api_key="your-api-key")
    parsed_doc = client.parse_document("path/to/document.pdf", isolate_tables=True)
    ```

- **Parser API Service** (`cneutral_doc.api.parser.service`):
  - **Purpose**: FastAPI service for document parsing with rate limiting and authentication.
  - **Key Endpoints**:
    - `POST /parse`: Parse a PDF document and return structured content.
  - **Running the Service**:
    ```python
    from cneutral_doc.api.parser.service import run_service

    # Start the service on localhost:8001
    run_service(host="127.0.0.1", port=8001, workers=4)
    ```

### ESG Analysis (Planned)

- **`Analyzer`** (`cneutral_doc.analyzer.analyzer`):
  - **Purpose**: Will analyze document content against ESG factors using Gemini AI to extract relevant insights.
  - **Key Methods** (planned):
    - `process_document(doc_path, factors_path)`: Will process a parsed document JSON against a set of ESG factors, returning analysis results.
  - **Usage** (future implementation):
    ```python
    from cneutral_doc.analyzer.analyzer import Analyzer

    # Will require GOOGLE_API_KEY in environment variables
    analyzer = Analyzer()
    results = analyzer.process_document("parsed_doc.json", "esg_factors.json")
    ```

The implemented classes can be combined in a pipeline to process ESG documents from PDF parsing through to table classification. The ESG analysis functionality is planned for future development. Check the `scripts/` directory for example workflows and demonstrations.

## Development

- **Code Style**: We follow PEP 8 with formatting enforced by Black and isort.
  ```bash
  # Format code with Black
  poetry run black .

  # Sort imports with isort
  poetry run isort .
  ```

- **Testing**: Tests are written with pytest. We've implemented a comprehensive testing strategy that balances thorough testing with fast execution:

  ### Test Categories

  Tests are categorized using pytest markers:

  - **Regular tests**: Fast unit tests that don't require special resources
  - **Slow tests** (`@pytest.mark.slow`): Tests that take longer to run but don't require GPU
  - **GPU tests** (`@pytest.mark.gpu`): Tests that require GPU resources

  ### Mocking Strategy

  Our tests use a sophisticated mocking strategy to avoid GPU-intensive operations:

  - GPU operations are mocked using `unittest.mock.patch`
  - Model loading is disabled using global fixtures in `tests/conftest.py`
  - External dependencies like `PdfConverter` and `TableConverter` are mocked
  - This approach allows thorough testing without requiring actual GPU resources

  ### Running Tests

  We provide a convenient script for running tests with different options:

  ```bash
  # Run all tests (including slow and GPU-intensive tests)
  ./scripts/run_tests.sh

  # Skip resource-intensive tests (recommended for development)
  ./scripts/run_tests.sh --skip-slow --skip-gpu

  # Run tests with coverage report
  ./scripts/run_tests.sh --coverage

  # Run specific test files or directories
  ./scripts/run_tests.sh --path tests/test_api

  # Run tests with verbose output
  ./scripts/run_tests.sh --verbose

  # Get help on all available options
  ./scripts/run_tests.sh --help
  ```

  ### When to Use Different Testing Approaches

  - **During development**: Use `--skip-slow --skip-gpu` for fast feedback cycles
  - **Before committing**: Run all tests to ensure comprehensive coverage
  - **In CI/CD pipelines**: Configure to run fast tests on every commit and all tests on scheduled runs
  - **For debugging specific issues**: Use `--path` to focus on relevant test files

  ### Test Performance

  Thanks to our mocking strategy, even tests marked as `slow` or `gpu` run quickly when executed. The markers are maintained to:

  1. Categorize tests by their resource requirements
  2. Allow selective testing during development
  3. Provide flexibility for different CI/CD environments
  4. Future-proof the test suite as new tests are added

- **Pre-commit Hooks**: The project uses pre-commit hooks to ensure code quality and test integrity:
  ```bash
  # Install pre-commit hooks
  poetry run pre-commit install
  ```

  The pre-commit hooks include:
  - **black**: Formats Python code
  - **isort**: Sorts Python imports
  - **nbqa-black/isort**: Formats Jupyter notebooks
  - **pytest**: Runs all tests to ensure code quality

  These hooks run automatically before each commit, ensuring that code is properly formatted and all tests pass. Thanks to our optimized testing strategy with comprehensive mocking, all tests run quickly enough to be included in the pre-commit process without skipping any.

- **Contributions**: Please follow conventional commits format for git messages:
  ```
  feat(component): add new feature
  fix(component): fix bug in component
  docs: update documentation
  style: formatting changes
  refactor: code restructuring without changing functionality
  test: add or update tests
  ```

## License

Proprietary License

This project is proprietary software. All rights reserved. Unauthorized copying, modification, distribution, or use of this software, via any medium, is strictly prohibited without express permission from the copyright holder.

## Contact

For questions, issues, or contributions, please: Open an issue in the project repository

## Roadmap

Future development plans include:
- Implementation of the ESG Analysis module using Google's Gemini AI
- Enhanced visualization capabilities for ESG insights
- Integration with cloud storage services for document management
- API improvements for scalability and performance
