"""
Base classes for table processing, classification, and visualization.

This module defines the interfaces for the table package components:
- BaseTableFilter: Interface for filtering and processing tables
- BaseTableClassifier: Interface for classifying tables into categories
- BaseVisualizer: Interface for visualizing tables
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from os import PathLike
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union

from pydantic import BaseModel, Field, validator

from cneutral_doc.document.parser.base import ParsedDocument, Table


@dataclass
class TableClassificationResult:
    """Result of table classification with ESG scores and table metadata."""

    e_score: float  # Environment score
    s_score: float  # Social score
    g_score: float  # Governance score
    other_score: float  # Other score
    numerical_density: float  # Proportion of cells containing numerical values
    rows: int  # Number of rows in the table
    cols: int  # Number of columns in the table
    is_valid_table: bool  # Whether the table is properly structured
    reasoning: Optional[str] = None  # Reasoning for the classification
    validation_message: Optional[str] = None  # Message explaining validation result
    validation_score: Optional[float] = None  # Score explaining validation result

    def to_dict(self) -> Dict[str, Any]:
        """Convert the result to a dictionary format."""
        result = {
            "e_score": round(self.e_score, 2),
            "s_score": round(self.s_score, 2),
            "g_score": round(self.g_score, 2),
            "other_score": round(self.other_score, 2),
            "numerical_density": round(self.numerical_density, 2),
            "rows": self.rows,
            "cols": self.cols,
            "is_valid_table": self.is_valid_table,
            "reasoning": self.reasoning,
            "validation_message": self.validation_message,
            "validation_score": self.validation_score,
        }

        return result

    def __post_init__(self):
        """Validate that confidence scores sum to 1."""
        total = self.e_score + self.s_score + self.g_score + self.other_score
        if not abs(total - 1.0) < 0.0001:
            raise ValueError(f"ESG and Other scores must sum to 1.0, got {total}")


class BaseTableClassifier(ABC):
    """Abstract base class for table classifiers."""

    @abstractmethod
    def classify_table(
        self,
        markdown_table: str,
    ) -> TableClassificationResult:
        """
        Classify a markdown table into ESG categories.

        Args:
            markdown_table: String containing markdown table

        Returns:
            TableClassificationResult with confidence scores and metadata
        """
        pass


class BaseTableFilter(ABC):
    """Abstract base class for table filtering components.

    This class defines the interface for components that process tables extracted
    from documents and classify or filter them based on specific criteria.
    """

    def __init__(self, **kwargs: Any) -> None:
        """Initialize the table filter with optional configuration parameters."""
        pass

    @abstractmethod
    def filter_tables(
        self, document: ParsedDocument, *args, **kwargs
    ) -> Dict[str, Any]:
        """Process and filter tables from a parsed document.

        Args:
            document: The parsed document containing tables to be filtered.
            *args: Additional positional arguments.
            **kwargs: Additional keyword arguments.

        Returns:
            A dictionary containing the filtered tables and their classifications.
        """
        pass


class BaseVisualizer(ABC):
    """Abstract base class for table visualizers."""

    @abstractmethod
    def visualize_table(
        self, markdown_table: str, esg_tag: Optional[str] = None
    ) -> str:
        """
        Generate React code with Plotly.js to visualize a markdown table.

        Args:
            markdown_table (str): The markdown table to visualize.
            esg_tag (str, optional): The ESG tag ('E', 'S', or 'G'). Defaults to None.

        Returns:
            str: React code that uses Plotly.js to visualize the table.
        """
        pass
