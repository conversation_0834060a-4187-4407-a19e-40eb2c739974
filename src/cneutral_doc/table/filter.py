# --- START OF FILE table_filter.py ---

import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union, cast

from pydantic import BaseModel

from cneutral_doc.document.parser.base import ParsedDocument, Table
from cneutral_doc.table.base import BaseTableFilter, TableClassificationResult
from cneutral_doc.table.classifier import GeminiTableClassifier

logger = logging.getLogger(__name__)


class TableFilter(BaseTableFilter):
    """Filters and classifies tables from parsed documents using an updated classifier.

    This class extracts tables from a parsed document, sends each table to a
    table classifier (which now uses flexible validation and returns a validation score)
    for analysis, stores the classification results, and applies filtering rules.
    """

    def __init__(
        self, api_key: str, classifier: Optional[GeminiTableClassifier] = None
    ) -> None:
        """Initialize the table filter with an optional table classifier.

        Args:
            api_key: API key for the classifier service.
            classifier: An instance of GeminiTableClassifier (using the updated code)
                to use for table classification. If None, a default classifier
                will be created.

        Raises:
            ValueError: If api_key is empty or None.
        """
        if not api_key:
            raise ValueError("API key cannot be empty or None")
        # Allows injecting a pre-configured classifier or creates a default one.
        self.classifier = classifier or GeminiTableClassifier(api_key=api_key)
        # Stores results internally before potential saving or selection.
        self.classification_results: Dict[str, Any] = {}

    def __save_results(self, output_path: Union[str, Path]) -> Dict[str, Any]:
        """Save classification results (including validation scores) to a JSON file.

        Args:
            output_path: Path where to save the JSON results.

        Returns:
            The serialized results dictionary that was saved to the file.

        Raises:
            IOError: If there's an error writing to the file.
            TypeError: If the data is not serializable to JSON (should be handled by Pydantic).
            ValueError: If the output path is invalid (handled by Pathlib).
        """
        if not self.classification_results:
            # Avoid writing empty files if no processing happened.
            logger.info("No results to save.")
            return {
                "tables": [],
                "classifications": {},
            }

        # create a serializable copy of the results
        serializable_results = {
            "tables": self.classification_results.get("tables", []),
            "classifications": {},
        }

        # Convert TableClassificationResult objects using their to_dict method
        for table_id, result in self.classification_results.get(
            "classifications", {}
        ).items():
            # --- Use the specific type check and its method ---
            if isinstance(result, TableClassificationResult):
                serializable_results["classifications"][
                    table_id
                ] = result.to_dict()  # Use the dedicated method
            elif isinstance(result, dict):
                # Keep existing dictionaries (e.g., error messages)
                serializable_results["classifications"][table_id] = result
            else:
                # Handle unexpected types
                logger.warning(
                    f"Unexpected type for classification result of table {table_id}: {type(result)}. Attempting string conversion."
                )
                serializable_results["classifications"][table_id] = str(result)

        # add filter scores if they exist
        if "filter_scores" in self.classification_results:
            serializable_results["filter_scores"] = self.classification_results[
                "filter_scores"
            ]

        try:
            output_path = Path(output_path)
            # Ensure the target directory exists before writing.
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with output_path.open("w", encoding="utf-8") as f:  # Specify encoding
                # Standard JSON saving with indentation for readability.
                json.dump(serializable_results, f, indent=4, ensure_ascii=False)
        except IOError as e:
            # Catch and log specific file writing errors.
            error_msg = f"Failed to write results to {output_path}: {str(e)}"
            logger.error(error_msg)
            raise IOError(error_msg) from e
        except TypeError as e:
            # Catch potential JSON serialization errors (less likely with Pydantic).
            error_msg = f"Failed to serialize results to JSON: {str(e)}"
            logger.error(error_msg)
            raise TypeError(error_msg) from e
        except Exception as e:
            # Catch any other unexpected errors during saving.
            error_msg = f"An unexpected error occurred while saving results to {output_path}: {str(e)}"
            logger.error(error_msg)
            raise RuntimeError(error_msg) from e

        logger.info(f"Results saved to {output_path}")
        return serializable_results

    def __classify_table(self, document: ParsedDocument) -> None:
        """Classify tables using the updated classifier and update internal state.

        Args:
            document: The parsed document containing tables to be classified.

        Raises:
            ValueError: If the document is None.
            RuntimeError: If all tables fail classification (due to exceptions in the loop).
        """
        if document is None:
            # Basic input validation.
            raise ValueError("Document cannot be None")
        if not document.tables:
            # Handle cases with no tables gracefully.
            logger.info("Document contains no tables to classify.")
            self.classification_results = {"tables": [], "classifications": {}}
            return

        logger.info(f"Starting classification for {len(document.tables)} tables.")
        table_classifications = {}
        classification_errors = []

        for table in document.tables:
            try:
                # Call the updated classifier's method. This now performs
                # flexible validation internally before attempting LLM classification.
                # The result is a TableClassificationResult object.
                classification_result: TableClassificationResult = (
                    self.classifier.classify_table(table.content)
                )

                # Store the full result object. It contains validation status,
                # scores, reasoning, dimensions, numerical density, and the validation score.
                # Use string index for JSON compatibility later.
                table_classifications[str(table.index)] = classification_result

                # Log success or validation failure status from the result
                if not classification_result.is_valid_table:
                    logger.warning(
                        f"Table {table.index} deemed invalid by classifier: {classification_result.validation_message}"
                    )
                elif (
                    classification_result.validation_message
                ):  # Handle cases where classification failed after validation
                    logger.warning(
                        f"Table {table.index} classified with issues: {classification_result.validation_message}"
                    )
                else:
                    logger.debug(f"Table {table.index} classified successfully.")

            except Exception as e:
                # Catch unexpected errors during the classification call itself.
                # This is different from validation/classification failures handled within classify_table.
                error_msg = f"Critical error during classification call for table {table.index}: {e}"
                logger.exception(
                    error_msg
                )  # Use logger.exception to include stack trace
                # Store error information for this table.
                table_classifications[str(table.index)] = {"error": error_msg}
                classification_errors.append((table.index, error_msg))

        # If all tables resulted in critical errors during the classification *call*
        if classification_errors and len(classification_errors) == len(document.tables):
            # Report if the entire process failed fundamentally for all tables.
            error_details = "; ".join(
                [f"Table {idx}: {err}" for idx, err in classification_errors]
            )
            raise RuntimeError(
                f"All tables failed during classification call: {error_details}"
            )

        # Store the results including original tables and the classification outcomes
        self.classification_results = {
            "tables": [
                {
                    "index": table.index,
                    "content": table.content,
                    "page_number": table.page_number,
                }
                for table in document.tables
            ],
            "classifications": table_classifications,  # Contains TableClassificationResult objects or error dicts
        }
        logger.info("Finished table classification phase.")

    def filter_tables(
        self,
        document: ParsedDocument,
        save_path: Optional[Union[str, Path]] = None,
        k: Optional[int] = None,
    ) -> Dict[str, Any]:
        """Process, classify (using updated classifier), and filter tables.

        Args:
            document: The parsed document containing tables.
            save_path: Optional path (str or Path) to save classification results.
            k: Number of top tables per ESG category based on filter score. If None, return all results.

        Returns:
            If k is None: Dictionary with all tables, classifications, and filter scores.
            If k is provided: Dictionary with ESG categories mapping to lists of top k table details.

        Raises:
            ValueError: If document is None or k is invalid.
            RuntimeError: If all tables fail classification calls.
            IOError/TypeError: If saving fails.
        """
        if document is None:
            # Basic input validation.
            raise ValueError("Document cannot be None")
        if k is not None and not isinstance(k, int) or (isinstance(k, int) and k <= 0):
            # Ensure k is a positive integer if provided.
            raise ValueError("Parameter k must be a positive integer")

        logger.info("Starting table filtering process...")

        # === Step 1: Classify Tables ===
        # This now uses the updated classifier with flexible validation.
        # Results are stored in self.classification_results.
        try:
            self.__classify_table(document)
        except RuntimeError as e:
            logger.error(f"Table classification phase failed: {e}")
            # Depending on requirements, you might want to return empty results or re-raise
            # Re-raising indicates a fundamental failure in processing the document.
            raise

        # === Step 2: Calculate Filter Scores ===
        # Apply domain-specific rules to rank potentially relevant tables.
        # This logic remains largely the same but operates on the results from the updated classifier.
        filter_scores: Dict[str, Dict[str, float]] = {"E": {}, "S": {}, "G": {}}
        processed_table_ids = set()  # Keep track of tables processed for scoring

        # Ensure classifications exist before proceeding
        classifications = self.classification_results.get("classifications", {})
        if not classifications:
            logger.warning("No classification results found after classification step.")
            # Handle case where __classify_table resulted in empty classifications but didn't raise error

        for table_id, classification_result in classifications.items():
            processed_table_ids.add(table_id)  # Mark as processed
            # Default values
            e_score, s_score, g_score = 0.0, 0.0, 0.0

            # --- Check 1: Is the result valid and not an error? ---
            # Ensure we have a valid classification result object before accessing attributes.
            if isinstance(classification_result, TableClassificationResult):
                # --- Check 2: Was the table deemed valid by the classifier? ---
                # Only consider tables that passed the classifier's validation (score-based).
                if not classification_result.is_valid_table:
                    logger.debug(
                        f"Skipping table {table_id}: Marked as invalid by classifier."
                    )
                    continue  # Skip invalid tables

                # --- Check 3: Is the primary category 'Other'? ---
                # Filter out tables not primarily E, S, or G based on scores.
                scores = {
                    "E": classification_result.e_score,
                    "S": classification_result.s_score,
                    "G": classification_result.g_score,
                    "O": classification_result.other_score,
                }
                # Find the category with the highest score
                # Handle ties: if 'O' ties with E/S/G, it's still considered 'Other' dominant here.
                highest_score = max(scores.values())
                primary_categories = [
                    cat for cat, score in scores.items() if score == highest_score
                ]

                if "O" in primary_categories:
                    logger.debug(
                        f"Skipping table {table_id}: Classified primarily as 'Other' (Score: {scores['O']:.2f})."
                    )
                    continue  # Skip 'Other' tables

                # --- Check 4: Does it meet numerical density threshold? ---
                # Apply domain rule to focus on data-rich tables.
                numerical_density = classification_result.numerical_density
                if numerical_density < 0.3:
                    logger.debug(
                        f"Skipping table {table_id}: Low numerical density ({numerical_density:.2f} < 0.3)."
                    )
                    continue  # Skip low density tables

                # --- Calculate Filter Score ---
                # Combine category score, size, and density for ranking.
                # Dimensions might be slightly different due to new validation, but formula is the same.
                rows = classification_result.rows
                cols = classification_result.cols
                e_score = scores["E"] * rows * cols * numerical_density
                s_score = scores["S"] * rows * cols * numerical_density
                g_score = scores["G"] * rows * cols * numerical_density

            elif (
                isinstance(classification_result, dict)
                and "error" in classification_result
            ):
                # Explicitly skip tables that resulted in an error during classification call.
                logger.debug(
                    f"Skipping table {table_id}: Encountered classification error."
                )
                continue
            else:
                # Handle unexpected formats in the results dictionary.
                logger.warning(
                    f"Skipping table {table_id}: Unexpected classification result format: {type(classification_result)}"
                )
                continue

            # --- Store Non-Zero Scores ---
            # Only store scores for categories where the table is relevant.
            if e_score > 1e-6:  # Use tolerance for float comparison
                filter_scores["E"][table_id] = e_score
            if s_score > 1e-6:
                filter_scores["S"][table_id] = s_score
            if g_score > 1e-6:
                filter_scores["G"][table_id] = g_score

        # Log if some tables were present but none were scored (e.g., all invalid, 'Other', or low density)
        if classifications and not any(filter_scores.values()):
            logger.warning(
                "No tables met the filtering criteria (valid, not 'Other', density >= 0.3) to receive a filter score."
            )

        # Add filter scores to the main results object
        self.classification_results["filter_scores"] = filter_scores

        # === Step 3: Save Results (Optional) ===
        if save_path:
            # Persist the detailed classification and filtering outcome.
            # This now uses the updated __save_results method that handles Pydantic models correctly.
            try:
                self.__save_results(save_path)
                logger.info(
                    f"All classification and filtering results saved to {save_path}"
                )
            except (IOError, TypeError, RuntimeError) as e:
                # Propagate saving errors if they occur
                logger.error(f"Failed to save results: {e}")
                raise

        logger.info("Finished filtering tables.")

        # === Step 4: Return Results (All or Top K) ===
        if k is not None:
            # Provide a ranked subset based on filter scores if requested.
            return self._select_top_k(k)
        else:
            # Return all processed information if no ranking is needed.
            return self.classification_results

    def _select_top_k(self, k: int = 1) -> Dict[str, List[Dict[str, Any]]]:
        """Select top k tables per ESG category based on calculated filter scores.

        Args:
            k: Number of top tables to select.

        Returns:
            Dictionary mapping ESG categories ('E', 'S', 'G') to lists of
            top table information dictionaries. Each dictionary contains:
            'table_id', 'filter_score', 'content', and 'classification' (as dict).

        Raises:
            ValueError: If k is not positive.
        """
        # Input validation for k.
        if not isinstance(k, int) or k <= 0:  # Check type again just in case
            raise ValueError("Parameter k must be a positive integer")

        # Check if results are available before trying to select.
        if (
            not hasattr(self, "classification_results")
            or not self.classification_results
        ):
            logger.warning(
                "No classification results available. Run filter_tables first."
            )
            return {"E": [], "S": [], "G": []}

        if "filter_scores" not in self.classification_results:
            # Ensure scores needed for ranking exist.
            logger.warning("Filter scores not calculated. Run filter_tables first.")
            return {"E": [], "S": [], "G": []}

        filter_scores = self.classification_results["filter_scores"]
        top_tables: Dict[str, List[Dict[str, Any]]] = {"E": [], "S": [], "G": []}

        # create a lookup dictionary for table content
        table_content_lookup = {
            str(table["index"]): table["content"]
            for table in self.classification_results.get("tables", [])
        }

        top_tables: Dict[str, List[Dict[str, Any]]] = {
            "E": [],
            "S": [],
            "G": [],
        }  # Initialize here

        for category in ["E", "S", "G"]:
            sorted_tables = sorted(
                filter_scores.get(category, {}).items(),
                key=lambda item: item[1],
                reverse=True,
            )
            top_k_for_category = sorted_tables[:k]

            for table_id, score in top_k_for_category:
                classification_data = self.classification_results.get(
                    "classifications", {}
                ).get(table_id, {})

                # Ensure classification data is a dictionary for consistent output
                classification_dict = {}
                # --- Use the specific type check and its method ---
                if isinstance(classification_data, TableClassificationResult):
                    classification_dict = (
                        classification_data.to_dict()
                    )  # Use the dedicated method
                elif isinstance(classification_data, dict):
                    classification_dict = (
                        classification_data  # Already a dict (e.g., error)
                    )
                else:
                    classification_dict = {
                        "error": f"Unknown classification format: {type(classification_data)}"
                    }

                content = table_content_lookup.get(table_id, "Content not found")

                table_info = {
                    "table_id": table_id,
                    "filter_score": score,
                    "content": content,
                    "classification": classification_dict,  # Now consistently uses the dict from to_dict()
                }
                top_tables[category].append(
                    table_info
                )  # Append to the initialized list

        return top_tables
