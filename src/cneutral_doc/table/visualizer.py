"""Implementation of the table visualizer using LangChain with various LLM models to generate React/Plotly.js code."""

import logging
import os
import re
from abc import abstractmethod
from typing import Any, Optional

from langchain_core.exceptions import LangChainException
from langchain_core.prompts import ChatPromptT<PERSON>plate
from langchain_google_genai import ChatGoogleGenerativeAI
from pydantic import BaseModel, Field, SecretStr, ValidationError

from cneutral_doc.table.base import BaseVisualizer

logger = logging.getLogger(__name__)


class VisualizationOutput(BaseModel):
    """Structured output for table visualization."""

    reasoning: str = Field(
        ...,
        description="Explanation of why this visualization type was chosen for the table data",
    )

    js_code: str = Field(
        ...,
        description="Valid, stand-alone React component code using Plotly.js that visualizes the table data. Do not include reasoning.",
    )


class LLMTableVisualizer(BaseVisualizer):
    """Base class for table visualizers using LangChain with various LLM models.

    This class defines the common interface and functionality for visualizing tables
    using LangChain and different LLM backends. It generates React code with Plotly.js
    that can be used to render interactive charts representing the table data.
    """

    def __init__(
        self, model_name: str, temp: float, api_key: str, *args, **kwargs
    ) -> None:
        """
        Initialize the LLM Table Visualizer.

        Args:
            model_name: Name of the LLM model to use.
            temp: Temperature for model generation. Lower values make output more deterministic.
            api_key: API key for the LLM provider.
            *args: Additional positional arguments.
            **kwargs: Additional keyword arguments.

        Raises:
            ValueError: If required parameters are missing or invalid.
        """
        super().__init__(*args, **kwargs)
        self.model_name = model_name
        self.temp = temp
        self.api_key = api_key
        self.chain = self._create_prompt_template() | self._setup_chat_model()

        logger.debug(f"Initialized LLMTableVisualizer with model: {model_name}")

    def _create_prompt_template(self) -> ChatPromptTemplate:
        """Create a prompt template for the LLM.

        Returns:
            ChatPromptTemplate: A configured prompt template for the visualization task.
        """
        template = """You are a data visualization expert. Given the following markdown table, generate React .js code that uses Plotly.js to create an appropriate visualization.

        The React component should be named {component_name}.

        Markdown Table:
        ```
        {markdown_table}
        ```
        
        Instructions:
        1. Carefully analyze the table structure and data. The columns and rows can be misaligned so make sure to align them correctly before generating the code.
        2. Split data into multiple plots if necessary. Make sure that any nested data relationships are maintained.
        3. Do not alter any numerical values in the table.
        4. Determine the most appropriate chart type (bar, line, scatter, pie, etc.) based on the data.
        5. Generate a React function (using the `function plot() {{ ... }}` syntax) that returns a React.createElement() tree, not JSX.  Do *not* use arrow functions.
        6. Do not include any imports or exports or dummy code.
        7. Make sure the visualization is responsive and has appropriate labels, titles, axes titles, and styling. Add titles to each plot.
        8. Make sure that the data is clearly visible and readable.
        9. If there are multiple plots, wrap them in a React.createElement('div', null, ...) container.
        10. Output ONLY VALID JSON.  The JSON should have the following format:

        ```json
        {{
          "reasoning": "REASONING_PLACEHOLDER",
          "js_code": "REACT_CODE"
        }}
        ```

        The `js_code` field should contain *only* the valid, stand-alone React code.  Do not include any explanation or reasoning in the `js_code`.  The React component *must* be defined as `function plot() {{ ... }}`, and *must not* be defined as `const plot = () => {{ ... }}`.
        """

        return ChatPromptTemplate.from_template(template)

    @abstractmethod
    def _setup_chat_model(self) -> Any:
        """Set up and configure the chat model for visualization.

        Returns:
            Any: Configured chat model instance
        """
        pass

    def visualize_table(
        self, markdown_table: str, esg_tag: Optional[str] = None
    ) -> str:
        """
        Generate React code with Plotly.js to visualize a markdown table.

        Args:
            markdown_table (str): The markdown table to visualize.
            esg_tag (str, optional): The ESG tag ('E', 'S', or 'G'). Defaults to None.

        Returns:
            str: React code that uses Plotly.js to visualize the table.

        Raises:
            ValueError: If the markdown_table is None, empty or ESG tag is invalid.
            TypeError: If markdown_table is not a string.
            RuntimeError: If there's an issue with the LLM API or processing.
        """
        logger.info("Starting table visualization process.")
        logger.debug(
            f"Input markdown table (first 200 chars):\n{markdown_table[:200]}..."
        )

        if markdown_table is None:
            raise ValueError("Input table cannot be None")
        if not isinstance(markdown_table, str):
            raise TypeError(f"Input table must be string, got {type(markdown_table)}")
        if not markdown_table.strip():
            raise ValueError("Input table cannot be empty")

        if esg_tag not in ["E", "S", "G", None]:
            logger.error(f"Invalid ESG tag: {esg_tag}")
            raise ValueError("ESG tag must be one of 'E', 'S', or 'G'")

        component_name = "plot"
        logger.info(f"Creating visualization with component name: {component_name}")

        try:
            logger.debug(
                f"Using LLM model: {self.model_name} with temperature: {self.temp}"
            )
            response = self.chain.invoke(
                {"component_name": component_name, "markdown_table": markdown_table}
            )

            logger.info("Successfully generated visualization code")
            logger.debug(f"Visualization reasoning: {response.reasoning}")

            return response.js_code

        except LangChainException as e:
            logger.error(f"LangChain error: {str(e)}")
            raise RuntimeError(f"Error generating visualization: {str(e)}") from e
        except ValidationError as e:
            logger.error(f"Output validation error: {str(e)}")
            raise RuntimeError(f"Invalid visualization output: {str(e)}") from e
        except Exception as e:
            logger.error(f"Unexpected error during visualization: {str(e)}")
            raise RuntimeError(
                f"Unexpected error during visualization: {str(e)}"
            ) from e


class GeminiTableVisualizer(LLMTableVisualizer):
    """Visualizer for markdown tables using LangChain with Google's Gemini model.

    This class implements the table visualization functionality using the
    Google Gemini model to generate React/Plotly.js code for visualizing tables.
    """

    def __init__(
        self,
        model_name: str = "gemini-2.0-flash",
        temp: float = 0.1,
        api_key: Optional[str] = None,
        *args,
        **kwargs,
    ) -> None:
        """
        Initialize the GeminiTableVisualizer.

        Args:
            model_name: Name of the Gemini model to use. Defaults to 'gemini-2.0-flash'.
            temp: Temperature for model generation. Lower values make output more deterministic.
            api_key: Google API key. If None, will use GOOGLE_API_KEY from environment.
            *args: Additional positional arguments to pass to the parent class.
            **kwargs: Additional keyword arguments to pass to the parent class.

        Raises:
            ValueError: If the API key is not provided and not available in environment.
        """

        api_key_str = (
            api_key if api_key is not None else os.environ.get("GOOGLE_API_KEY", "")
        )

        if not api_key_str:
            raise ValueError(
                "API key must be provided either as a parameter or through the GOOGLE_API_KEY environment variable"
            )

        super().__init__(
            model_name=model_name, temp=temp, api_key=api_key_str, *args, **kwargs
        )

    def _setup_chat_model(self) -> Any:
        """
        Set up and configure the Google Gemini chat model for visualization.

        Returns:
            Any: Configured ChatGoogleGenerativeAI model for visualization.

        Raises:
            RuntimeError: If there's an issue setting up the Gemini model.
        """
        try:
            logger.debug(f"Setting up Gemini model: {self.model_name}")
            chat_model = ChatGoogleGenerativeAI(
                model=self.model_name,
                temperature=self.temp,
                api_key=SecretStr(self.api_key),
                convert_system_message_to_human=True,
            ).with_structured_output(VisualizationOutput, method="json_mode")
            return chat_model
        except Exception as e:
            logger.error(f"Failed to set up Gemini model: {str(e)}")
            raise RuntimeError(f"Error setting up Gemini model: {str(e)}") from e