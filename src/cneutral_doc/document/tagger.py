"""Document tagging module for metadata extraction from documents.

This module provides classes for extracting structured metadata from documents
using various LLM providers, with a focus on document classification and
information extraction.

Classes:
    TaggingOutput: Pydantic model for document metadata classification
    BaseDocTagger: Abstract base class for document tagging implementations
    LLMDocTagger: Base class for LLM-based document taggers
    OpenAIDocTagger: OpenAI-specific document tagger implementation

Typical usage:
    tagger = OpenAIDocTagger()
    metadata = tagger.tag_document("path/to/document.pdf")
"""

import logging
from abc import ABC, abstractmethod
from pathlib import Path
from typing import Dict, Literal, Union

import pdfplumber
from langchain.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
from pydantic import BaseModel, Field, ValidationError

# Configure module logger
logger = logging.getLogger(__name__)


class DocumentTaggingError(Exception):
    """Base exception for document tagging errors."""


class DocumentProcessingError(DocumentTaggingError):
    """Raised when document processing fails."""


class ModelInferenceError(DocumentTaggingError):
    """Raised when model inference fails."""


class DocumentSizeError(DocumentProcessingError):
    """Raised when document size exceeds maximum allowed size."""


DOCUMENT_TYPES = ["annual_report", "esg_report", "integrated_report", "other"]


class TaggingOutput(BaseModel):
    """Pydantic model for document metadata classification.

    This model defines the structured output format for document classification,
    including company information, document metadata, and document type classification.
    All fields support None values except doc_type which requires a valid document
    type enum value.

    Attributes:
        company_name: Official full English company name
        document_title: Complete document title
        language: ISO 639-1 two-letter language code
        publication_year: Four-digit publication year
        doc_type: Document type classification
    """

    company_name: str | None = Field(
        default=None,
        description="Official full English name of the company from Wikipedia, e.g., Shell plc instead of Shell",
    )
    document_title: str | None = Field(
        default=None, description="Official title of the document provided"
    )
    language: str | None = Field(
        default=None,
        description="Main language of the document using ISO 639-1 two-letter code (e.g., 'en' for English, 'es' for Spanish, 'zh' for Chinese)",
    )
    publication_year: str | None = Field(
        default=None, description="Year of publication in 4 digits, e.g., '2021'"
    )
    doc_type: Literal["annual_report", "esg_report", "integrated_report", "other"] = (
        Field(
            description=(
                "Inferred document type based on the title, the table of contents and other contents provided:\n"
                "- annual_report: Annual financial reports covering a full year\n"
                "- esg_report: ESG, impact, sustainability reports\n"
                "- integrated_report: Combined annual and ESG reports\n"
                "- other: Other document types"
            )
        )
    )


class BaseDocTagger(ABC):
    """Abstract base class for document tagging implementations."""

    @abstractmethod
    def tag_text(self, text: str) -> Dict:
        """Extract tags and metadata from raw text.

        Args:
            text: Raw text to analyze

        Returns:
            Dictionary containing extracted metadata

        Raises:
            DocumentTaggingError: If metadata extraction fails
        """

    @abstractmethod
    def tag_document(self, document_path: str) -> Dict:
        """Extract tags and metadata from a parsed document.

        Args:
            document_path: Path to the document to analyze

        Returns:
            Dictionary containing extracted metadata

        Raises:
            DocumentTaggingError: If document processing or metadata extraction fails
        """


class LLMDocTagger(BaseDocTagger):
    MAX_FILE_SIZE_MB = 200  # Example size limit

    def _validate_file_size(self, file_path: Path) -> None:
        """Validate that the file size is within acceptable limits.

        Args:
            file_path: Path to the document

        Raises:
            DocumentSizeError: If file size exceeds maximum allowed size
        """
        size_mb = file_path.stat().st_size / (1024 * 1024)
        if size_mb > self.MAX_FILE_SIZE_MB:
            raise DocumentSizeError(
                f"File size ({size_mb:.1f}MB) exceeds maximum allowed size "
                f"of {self.MAX_FILE_SIZE_MB}MB"
            )

    def __init__(self) -> None:
        """Initialize the LLM document tagger."""
        logger.debug("Initializing LLMDocTagger")
        self._chain = None
        self.prompt_template = ChatPromptTemplate.from_template(
            """You are a document classification expert. Analyze the provided text and extract key metadata about the document.

Required: Extract the following information from the given text:
1. Company Name: The official full English company name (e.g., "Shell plc" not just "Shell")
2. Document Title: The complete title as shown in the document
3. Language: The main language code (e.g., 'en', 'es', 'zh')
4. Publication Year: The document's year in YYYY format
5. Document Type: Classify into one of these categories:
   - annual_report: Full-year financial reports (excluding partial reports/letters)
   - esg_report: ESG/sustainability reports (excluding indices)
   - integrated_report: Combined annual and ESG reports
   - other: Documents not fitting above categories

Important:
- Focus on the first few pages, including cover, title page, and table of contents
- If information is not found, return null/None
- Be precise with company names, using official full names
- Verify the year is actually the publication year, not just any year mentioned

Text to analyze:
{input}
"""
        )

    @abstractmethod
    def _get_llm_chain(self):
        """Initialize and return the LLM chain.

        Returns:
            Configured LLM chain

        Raises:
            ModelInferenceError: If chain initialization fails
        """
        raise NotImplementedError("Subclasses must implement _get_llm_chain")

    @property
    def chain(self):
        """Lazy initialization of LLM chain."""
        if self._chain is None:
            self._chain = self._get_llm_chain()
            if self._chain is None:
                raise ModelInferenceError(
                    "LLM chain initialization failed, _get_llm_chain returned None"
                )
        return self._chain

    def _extract_initial_pages(
        self, document_path: Union[str, Path], k: int = 3
    ) -> str:
        """Extract and combine text from the first k pages of a PDF document.

        Args:
            document_path: Path to the PDF document
            k: Number of initial pages to extract (default: 3)

        Returns:
            Combined text from the first k pages, or empty string if no text found

        Raises:
            DocumentProcessingError: If PDF extraction fails
            DocumentSizeError: If file size exceeds maximum allowed size
        """
        try:
            path = Path(document_path)
            if not path.exists():
                raise FileNotFoundError(f"Document not found: {document_path}")

            # Let DocumentSizeError propagate up directly
            self._validate_file_size(path)

            text = []
            logger.debug(f"Extracting first {k} pages from {path}")
            with pdfplumber.open(document_path) as pdf:
                for i in range(min(k, len(pdf.pages))):
                    page_text = pdf.pages[i].extract_text() or ""
                    text.append(page_text)
                    logger.debug(f"Extracted page {i+1}: {len(page_text)} characters")

            if all(t == "" for t in text):
                logger.warning(f"No text extracted from first {k} pages")
                return ""

            return "\n\n".join(text)
        except DocumentSizeError:
            # Re-raise DocumentSizeError without wrapping
            raise
        except Exception as e:
            logger.error(f"Failed to extract pages: {str(e)}", exc_info=True)
            raise DocumentProcessingError(f"Failed to extract pages: {str(e)}") from e

    def tag_document(self, document_path: str) -> Dict:
        """Extract tags and metadata from a parsed document.

        Args:
            document_path: Path to the PDF document to analyze

        Returns:
            Dictionary containing extracted metadata and tags

        Raises:
            DocumentProcessingError: If document processing fails
            ModelInferenceError: If model inference fails
            DocumentSizeError: If file size exceeds maximum allowed size
        """
        try:
            logger.info(f"Processing document: {document_path}")
            initial_text = self._extract_initial_pages(document_path)
            if not initial_text:
                raise DocumentProcessingError("No text content extracted from document")

            return self.tag_text(initial_text)
        except (DocumentSizeError, DocumentProcessingError) as e:
            # Let these specific errors propagate up
            raise
        except Exception as e:
            logger.error(f"Error processing document: {str(e)}", exc_info=True)
            raise DocumentProcessingError(f"Error processing document: {str(e)}") from e

    def tag_text(self, text: str) -> Dict:
        """Extract tags and metadata from raw text.

        Args:
            text: Raw text to analyze

        Returns:
            Dictionary containing extracted metadata

        Raises:
            ModelInferenceError: If model inference or validation fails
        """
        try:
            logger.debug("Processing text with LLM chain")
            result = self.chain.invoke({"input": text})
            return result.model_dump()
        except ValidationError as e:
            logger.error(f"Validation error in tag_text: {str(e)}", exc_info=True)
            raise ModelInferenceError(f"Validation error in tag_text: {str(e)}") from e
        except Exception as e:
            logger.error(f"Model inference error: {str(e)}", exc_info=True)
            raise ModelInferenceError(f"Model inference error: {str(e)}") from e


class OpenAIDocTagger(LLMDocTagger):
    """OpenAI-specific document tagger implementation."""

    def __init__(
        self, model_name: str = "gpt-4o-mini-2024-07-18", temperature: float = 0
    ) -> None:
        """Initialize OpenAI tagger with specific model configuration.

        Args:
            model_name: Name of the OpenAI model to use
            temperature: Sampling temperature for model inference
        """
        logger.info(f"Initializing OpenAIDocTagger with model {model_name}")
        super().__init__()
        self.model_name = model_name
        self.temperature = temperature

    def _get_llm_chain(self):
        """Initialize and return the OpenAI-specific LLM chain.

        Returns:
            Configured OpenAI LLM chain

        Raises:
            ModelInferenceError: If chain initialization fails
        """
        try:
            logger.debug("Creating OpenAI LLM chain")
            llm = ChatOpenAI(
                model=self.model_name,
                temperature=self.temperature,
            ).with_structured_output(TaggingOutput, method="function_calling")
            return self.prompt_template | llm
        except Exception as e:
            logger.error(f"Failed to create LLM chain: {str(e)}", exc_info=True)
            raise ModelInferenceError(f"Failed to create LLM chain: {str(e)}") from e
