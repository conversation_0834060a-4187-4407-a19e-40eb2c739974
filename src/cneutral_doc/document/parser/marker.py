"""Module for parsing PDF documents using the Marker library.

This module provides the MarkerParser class which implements the DocumentParser
interface for parsing PDF documents into structured content including text, tables,
and images.
"""

import logging
import re
from pathlib import Path
from typing import Any, Dict, List, Optional, Pattern, Tuple, Union

from marker.config.parser import ConfigParser
from marker.converters.pdf import PdfConverter
from marker.converters.table import TableConverter
from marker.models import create_model_dict
from marker.output import save_output, text_from_rendered

from .base import DocumentParser, Page, ParsedDocument, Table

logger = logging.getLogger(__name__)


class MarkerParser(DocumentParser):
    """Parser implementation using Marker library for PDF document processing.

    This class provides methods to extract and structure content from PDF documents
    including text, tables, images and metadata.

    Attributes:
        pdf_path: Path to the PDF file being processed.
        config: Configuration dictionary for the parser.
        config_parser: ConfigParser instance for handling parser configuration.
        pdf_converter: Converter instance for PDF text extraction.
        table_converter: Converter instance for PDF table extraction.
    """

    def __init__(self, format: str = "markdown") -> None:
        """Initialize MarkerParser with specified output format.

        Args:
            format: Output format for parsed content. Defaults to "markdown".
        """
        super().__init__()
        self.pdf_path: Optional[Path] = None
        self.config = {
            "output_format": format,
            "model_name": "default",
            "model_type": "base",
            "paginate_output": True,
        }
        self.config_parser = ConfigParser(self.config)
        config_dict = self.config_parser.generate_config_dict()
        artifact_dict = create_model_dict()

        self.pdf_converter = PdfConverter(
            config=config_dict,
            artifact_dict=artifact_dict,
        )
        self.table_converter = TableConverter(
            config=config_dict,
            artifact_dict=artifact_dict,
        )

    @staticmethod
    def _get_span_regex() -> Pattern:
        """Get regex pattern for page span markers."""
        return re.compile(r'<span id="page-(\d+)-\d+">')

    @staticmethod
    def _get_image_regex() -> Pattern:
        """Get regex pattern for image markers."""
        return re.compile(r"!\[.*?\]\(.*?_page_(\d+)_.*?\)")

    # def __split_tables(self, markdown_text: str) -> List[Table]:
    #     """Split markdown text into individual tables.

    #     Args:
    #         markdown_text: String containing markdown-formatted tables.

    #     Returns:
    #         List of Table objects. Returns empty list if no tables found.
    #     """
    #     if not markdown_text or not markdown_text.strip():
    #         return []

    #     tables = [
    #         table.strip()
    #         for table in markdown_text.split("\n\n")
    #         if table.strip() and "|" in table
    #     ]

    #     return [Table(content=table, index=i) for i, table in enumerate(tables)]

    def __split_markdown_by_page(
        self,
        markdown_text: str,
        isolate_tables: bool = True,
        auto_isolate: bool = False,
    ) -> Tuple[List[Page], List[Table]]:
        """Split markdown text into individual pages and tables based on page markers.

        The text is expected to be paginated using the format:

        {page_number}----------------------------------------------------------------

        {page_content}

        Args:
            markdown_text: String containing markdown-formatted text with page markers.
            isolate_tables: If True, extract and parse tables separately.
            auto_isolate: If True, isolate tables automatically based on the content, else manually isolate tables.

        Returns:
            Tuple containing:
                - List of Page objects in order of appearance.
                - List of Table objects in order of appearance.

        Raises:
            TypeError: If markdown_text is not a string.
            ValueError: If page markers are malformed or out of order.
        """
        if not isinstance(markdown_text, str):
            raise TypeError("markdown_text must be a string")

        # split the text by the page marker pattern
        # split_pattern = r"{(\d+)}------------------------------------------------\n\n"
        split_pattern = r"{(\d+)}" + r"-" * 48 + r"\n\n"
        pages = re.split(split_pattern, markdown_text)

        if pages[0] == "":
            pages = pages[1:]

        # process the pages
        final_pages = []
        final_tables = []
        for i in range(0, len(pages), 2):
            page_number = int(pages[i])
            content = pages[i + 1].strip()  # still contains the table markdown

            if isolate_tables:
                if auto_isolate:
                    # tables = self._parse_tables()
                    # split_tables = self.__split_tables(tables)
                    raise NotImplementedError(
                        "Auto-isolation of tables is not supported as the table converter does not preserve page numbers. "
                        "Please use manual isolation (auto_isolate=False) instead."
                    )
                else:
                    split_tables = self._manual_isolate_tables(content, page_number + 1)
            else:
                split_tables = []

            final_pages.append(
                Page(
                    content=content,
                    page_number=page_number + 1,  # convert to 1-based page numbers
                )
            )
            final_tables.extend(split_tables)
        # sort pages by page number to ensure correct order
        final_pages.sort(key=lambda x: x.page_number)
        final_tables.sort(key=lambda x: x.page_number)
        return final_pages, final_tables

    def is_supported(self, file_path: Union[str, Path]) -> bool:
        """Check if the given file is supported by this parser.

        Args:
            file_path: Path to the file to check.

        Returns:
            True if file has .pdf extension, False otherwise.
        """
        return Path(file_path).suffix.lower() == ".pdf"

    def parse(
        self,
        file_path: Union[str, Path],
        isolate_tables: bool = True,
        auto_isolate: bool = False,
    ) -> ParsedDocument:
        """Parse a PDF document into structured content.

        Args:
            pdf_path: Path to the PDF file to parse.
            isolate_tables: If True, extract and parse tables separately.
            auto_isolate: If True, isolate tables automatically based on the content, else manually isolate tables.
        Returns:
            ParsedDocument containing structured content.

        Raises:
            ValueError: If PDF parsing fails.
            RuntimeError: If unexpected error occurs during parsing.
        """
        print("Parsing document...")
        self.pdf_path = Path(file_path)
        pdf_name = Path(file_path).stem
        if not self.pdf_path.exists():
            raise ValueError(f"PDF file not found: {self.pdf_path}")

        try:
            text, images, metadata = self._parse_text()
            split_pages, split_tables = self.__split_markdown_by_page(
                text, isolate_tables, auto_isolate
            )

            print("Successfully parsed document.")

            return ParsedDocument(
                name=pdf_name,
                pages=split_pages,
                metadata=metadata,
                raw_data=text,
                images=images or [],
                tables=split_tables,
            )

        except Exception as e:
            logger.error("Failed to parse PDF: %s", str(e))
            raise RuntimeError(f"Failed to parse PDF: {str(e)}") from e

    def _manual_isolate_tables(
        self, markdown_text: str, page_number: int
    ) -> List[Table]:
        """Manually isolate tables from the parsed text.

        Args:
            markdown_text: String containing markdown-formatted text.
            page_number: The page number of the text.

        Returns:
            List of Table objects.
        """
        lines = markdown_text.split("\n")

        tables = []
        current_table = []
        in_table = False

        for i, line in enumerate(lines):
            # check if the line could be part of a table
            is_table_line = "|" in line

            # special case for separator line (contains dashes and pipes)
            is_separator_line = is_table_line and all(
                c == "|" or c == "-" or c == ":" or c.isspace() for c in line
            )

            # if this is a table line
            if is_table_line:
                # if we weren't in a table before, start a new one
                if not in_table:
                    in_table = True
                    current_table = [line]
                # otherwise add to the current table
                else:
                    current_table.append(line)
            # if not a table line but we were in a table
            elif in_table:
                # check if this is an empty line that should end the table
                if line.strip() == "":
                    tables.append("\n".join(current_table))
                    current_table = []
                    in_table = False
                # if it's not an empty line but doesn't have a pipe, end of table
                else:
                    tables.append("\n".join(current_table))
                    current_table = []
                    in_table = False

        # check for table at the end of the document
        if in_table and current_table:
            tables.append("\n".join(current_table))

        return [
            Table(content=table, index=i, page_number=page_number)
            for i, table in enumerate(tables)
        ]

    def _parse_tables(self) -> str:
        """Extract tables from PDF using TableConverter.

        Returns:
            Extracted table content in specified format.

        Raises:
            ValueError: If PDF path is not set or table extraction fails.
        """
        if not self.pdf_path:
            raise ValueError("PDF path not set")
        try:
            rendered = self.table_converter(str(self.pdf_path))
            text, _, _ = text_from_rendered(rendered)
            return text
        except Exception as e:
            logger.error("Failed to parse tables: %s", str(e))
            raise ValueError(f"Table extraction failed: {str(e)}") from e

    def _parse_text(self) -> Tuple[str, List[Dict], Dict]:
        """Extract text and images from PDF using PdfConverter.

        Returns:
            Tuple containing:
                - text content (str)
                - list of images (List[Dict])
                - metadata dictionary (Dict)

        Raises:
            ValueError: If PDF path is not set or text extraction fails.
        """
        if not self.pdf_path:
            raise ValueError("PDF path not set")
        try:
            rendered = self.pdf_converter(str(self.pdf_path))
            text, _, images = text_from_rendered(rendered)
            metadata = rendered.metadata or {}
            return text, list(images) or [], metadata
        except Exception as e:
            logger.error("Failed to parse text: %s", str(e))
            raise ValueError(f"Text extraction failed: {str(e)}") from e

    def _save_tables(self, rendered: Any, output_dir: Union[str, Path]) -> None:
        """Save extracted tables to output directory.

        Args:
            rendered: Rendered table content.
            output_dir: Directory to save tables.

        Raises:
            ValueError: If saving tables fails.
        """
        try:
            output_dir = Path(output_dir)
            pdf_name = self.pdf_path.stem if self.pdf_path else "unknown"
            save_output(rendered, str(output_dir), f"{pdf_name}_tables")
        except Exception as e:
            logger.error("Failed to save tables: %s", str(e))
            raise ValueError(f"Failed to save tables: {str(e)}") from e

    def _save_text(self, rendered: Any, output_dir: Union[str, Path]) -> None:
        """Save extracted text to output directory.

        Args:
            rendered: Rendered text content.
            output_dir: Directory to save text.

        Raises:
            ValueError: If saving text fails.
        """
        try:
            output_dir = Path(output_dir)
            pdf_name = self.pdf_path.stem if self.pdf_path else "unknown"
            save_output(rendered, str(output_dir), f"{pdf_name}_text")
        except Exception as e:
            logger.error("Failed to save text: %s", str(e))
            raise ValueError(f"Failed to save text: {str(e)}") from e

    @staticmethod
    def from_dict(data: Dict[str, Any]) -> ParsedDocument:
        """Reconstruct a ParsedDocument object from a dictionary.

        This static method creates a ParsedDocument object from a dictionary
        that was previously created using the to_dict() method. It handles
        the case where the 'images' field might be missing in the saved JSON.

        Args:
            data: Dictionary containing ParsedDocument data.

        Returns:
            ParsedDocument: Reconstructed ParsedDocument object.

        Raises:
            ValueError: If the dictionary is missing required fields.
        """
        if not isinstance(data, dict):
            raise ValueError("Input must be a dictionary")

        required_fields = ["name", "pages", "metadata", "raw_data"]
        for field in required_fields:
            if field not in data:
                raise ValueError(f"Missing required field: {field}")

        # reconstruct Page objects
        pages = [
            Page(
                content=page_data["content"],
                page_number=page_data["page_number"],
                metadata=page_data.get("metadata", {}),
            )
            for page_data in data["pages"]
        ]

        # reconstruct Table objects if present
        tables = [
            Table(
                content=table_data["content"],
                index=i,  # Use the index in the list as the table index
                page_number=table_data["page_number"],
            )
            for i, table_data in enumerate(data.get("tables", []))
        ]

        # create ParsedDocument with empty images list if not provided
        return ParsedDocument(
            name=data["name"],
            pages=pages,
            metadata=data["metadata"],
            raw_data=data["raw_data"],
            tables=tables,
            images=data.get(
                "images", []
            ),  # default to empty list if images not present
        )
