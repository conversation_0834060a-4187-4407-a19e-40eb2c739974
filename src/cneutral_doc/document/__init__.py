"""Document processing module for cneutral-doc.

This module provides functionality for document processing, including:
- Document parsing: Converting documents to structured formats
- Document tagging: Extracting metadata and classifying documents

The module is organized into submodules:
- parser: Document parsing functionality
- tagger: Document tagging and classification
"""

from .tagger import (
    BaseDocTagger,
    DocumentProcessingError,
    DocumentSizeError,
    DocumentTaggingError,
    LLMDocTagger,
    ModelInferenceError,
    OpenAIDocTagger,
    TaggingOutput,
)

__all__ = [
    # Tagger classes
    "BaseDocTagger",
    "LLMDocTagger",
    "OpenAIDocTagger",
    "TaggingOutput",
    # Exceptions
    "DocumentTaggingError",
    "DocumentProcessingError",
    "ModelInferenceError",
    "DocumentSizeError",
]
