"""API service for CNeutral document parsing."""

# Standard library imports
import logging
import os
from pathlib import Path
from tempfile import NamedTemporaryFile
from typing import Any, Dict, List, Literal

import uvicorn

# Third-party imports
from dotenv import load_dotenv
from fastapi import (
    Depends,
    FastAPI,
    File,
    HTTPException,
    Request,
    Security,
    UploadFile,
)
from fastapi.security.api_key import APIKeyHeader
from pydantic import BaseModel, Field
from slowapi import Limiter
from slowapi.util import get_remote_address
from starlette.status import HTTP_403_FORBIDDEN

# Local imports
from cneutral_doc.document.parser.marker import MarkerParser
from cneutral_doc.utils import get_project_root, setup_logging

# Module logger - configuration should be handled by the application
logger = logging.getLogger(__name__)


class Page(BaseModel):
    """Matches Page class from parser.base."""

    content: str
    page_number: int
    metadata: Dict[str, Any] = Field(default_factory=dict)


class Table(BaseModel):
    """Matches Table class from parser.base."""

    content: str
    index: int
    page_number: int


class ParsedDocumentResponse(BaseModel):
    """API response model matching ParsedDocument structure."""

    name: str
    pages: List[Page]
    metadata: Dict[str, Any]
    raw_data: Any
    tables: List[Table] = Field(default_factory=list)

    class Config:
        populate_by_name = True  # Updated for Pydantic V2


def load_api_key() -> str:
    """Load and validate API key from environment."""
    env_path = Path(get_project_root()) / ".env"
    if not load_dotenv(dotenv_path=env_path):
        logger.warning(f"No .env file found at {env_path}")

    api_key = os.getenv("CNEUTRAL_API_KEY")
    if not api_key:
        raise ValueError(
            "CNEUTRAL_API_KEY environment variable is not set. "
            "Please set it in your .env file or environment."
        )
    return api_key


# Initialize API components
API_KEY = load_api_key()

# Initialize FastAPI app
app = FastAPI(
    title="CNeutral Document Parser API",
    description="API for parsing PDF documents",
    version="1.0.0",
)

# Configure API security
api_key_header = APIKeyHeader(name="X-API-Key", auto_error=True)

# Configure rate limiting
limiter = Limiter(key_func=get_remote_address)
app.state.limiter = limiter


async def verify_api_key(api_key_header: str = Security(api_key_header)) -> str:
    """Verify the API key from the request header."""
    if not api_key_header or api_key_header != API_KEY:
        logger.warning(f"Invalid API key attempt")
        raise HTTPException(
            status_code=HTTP_403_FORBIDDEN, detail="Invalid or missing API key"
        )
    return api_key_header


FormatType = Literal["markdown", "text", "html"]


@app.post("/parse", response_model=ParsedDocumentResponse)
async def parse_document(
    request: Request,
    file: UploadFile = File(...),
    isolate_tables: bool = True,
    format: FormatType = "markdown",
    api_key: str = Depends(verify_api_key),
) -> ParsedDocumentResponse:
    """Parse a PDF document and return structured content.

    Args:
        request: FastAPI request object
        file: PDF file to parse
        isolate_tables: Whether to extract tables separately
        format: Output format for parsed content ("markdown", "text", or "html")
        api_key: API key for authentication

    Raises:
        HTTPException: With appropriate status codes:
            - 400: Invalid file format or empty file
            - 422: Invalid format parameter
            - 500: Server-side processing error
    """
    logger.info(
        f"Received parse request for file: {file.filename} with format: {format}"
    )

    # Validate file format
    if not (file.filename and file.filename.lower().endswith(".pdf")):
        logger.warning(f"Invalid file format attempted: {file.filename}")
        raise HTTPException(
            status_code=400, detail="Invalid file format. Only PDF files are supported."
        )

    temp_file_path = None
    try:
        # Read and validate file content
        content = await file.read()
        if not content:
            logger.warning("Empty file received")
            raise HTTPException(status_code=400, detail="Empty file received")

        # Save to temporary file
        with NamedTemporaryFile(delete=False, suffix=".pdf") as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name

        logger.debug(
            f"Starting document parsing with MarkerParser, isolate_tables={isolate_tables}"
        )

        # Parse document
        try:
            parser = MarkerParser(format=format)
            parsed_doc = parser.parse(temp_file_path, isolate_tables=isolate_tables)
        except ValueError as e:
            logger.error(f"Parser error: {str(e)}")
            raise HTTPException(
                status_code=400, detail=f"Failed to parse document: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected parser error: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500, detail="Internal server error during document parsing"
            )

        logger.info(
            f"Successfully parsed document {file.filename} with {len(parsed_doc.pages)} pages"
        )

        # Construct response using ParsedDocument's to_dict method
        try:
            # Convert ParsedDocument to dict using its to_dict method
            doc_dict = parsed_doc.to_dict()

            # Create response model from the dictionary
            return ParsedDocumentResponse(**doc_dict)
        except (KeyError, TypeError, AttributeError) as e:
            logger.error(f"Error constructing response: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=500,
                detail="Error constructing response from parsed document",
            )

    except HTTPException:
        raise  # Re-raise HTTP exceptions as-is
    except Exception as e:
        logger.error(f"Unhandled error: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="Internal server error")
    finally:
        # Clean up temporary file
        if temp_file_path:
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"Failed to delete temporary file: {e}")


def run_service(
    host: str = "127.0.0.1", port: int = 8001, workers: int = 4, reload: bool = False
) -> None:
    """Run the parser service."""

    setup_logging(log_level=logging.INFO)

    logger.info(f"Starting Parser Service on {host}:{port}")
    uvicorn.run(
        "cneutral_doc.api.parser.service:app",
        host=host,
        port=port,
        workers=workers,
        reload=reload,
        proxy_headers=True,
        forwarded_allow_ips="*",
    )


if __name__ == "__main__":
    run_service()
