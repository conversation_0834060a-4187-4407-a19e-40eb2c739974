default_language_version:
    python: python3
default_stages: [pre-commit]

repos:
-   repo: local
    hooks:
    -   id: black
        name: black
        description: Format Python code with black
        entry: poetry run black
        language: system
        types: [python]

    -   id: isort
        name: isort
        description: Sort Python imports with isort
        entry: poetry run isort
        language: system
        types: [python]
        args: ["--profile", "black"]

    -   id: nbqa-isort
        name: nbqa-isort
        description: Run isort on Jupyter notebooks via nbqa
        entry: bash -c 'poetry run nbqa isort "$@" --profile black' --
        language: system
        types: [jupyter]

    -   id: nbqa-black
        name: nbqa-black
        description: Run black on Jupyter notebooks via nbqa
        entry: bash -c 'poetry run nbqa black "$@"' --
        language: system
        types: [jupyter]

    -   id: pytest
        name: pytest
        description: Run all tests to ensure code quality
        entry: ./scripts/run_tests.sh
        language: system
        pass_filenames: false
        always_run: true
        verbose: true
