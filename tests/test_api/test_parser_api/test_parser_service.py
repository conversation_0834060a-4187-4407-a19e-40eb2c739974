"""Tests for the API service module using async approach."""

import os
from unittest.mock import Magic<PERSON>ock, patch

import pytest
from fastapi import HTT<PERSON><PERSON>x<PERSON>, UploadFile
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_403_FORBIDDEN,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from cneutral_doc.api.parser.service import (
    ParsedDocumentResponse,
    parse_document,
    verify_api_key,
)


# Define AsyncMock class for mocking async methods
class AsyncMock(MagicMock):
    async def __call__(self, *args, **kwargs):
        return super(AsyncMock, self).__call__(*args, **kwargs)


@pytest.fixture
def mock_api_key():
    """Mock API key for testing."""
    with patch.dict(os.environ, {"CNEUTRAL_API_KEY": "test-api-key"}):
        yield "test-api-key"


@pytest.mark.asyncio
async def test_verify_api_key_valid(mock_api_key):
    """Test API key verification with valid key."""
    with patch("cneutral_doc.api.parser.service.API_KEY", "test-api-key"):
        result = await verify_api_key(mock_api_key)
        assert result == mock_api_key


@pytest.mark.asyncio
async def test_verify_api_key_invalid():
    """Test API key verification with invalid key."""
    with patch("cneutral_doc.api.parser.service.API_KEY", "test-api-key"):
        with pytest.raises(HTTPException) as exc_info:
            await verify_api_key("wrong-key")
        assert exc_info.value.status_code == HTTP_403_FORBIDDEN
        assert "Invalid or missing API key" in exc_info.value.detail


@pytest.mark.asyncio
async def test_parse_document_success(
    mock_api_key, mock_pdf_file, mock_parsed_document
):
    """Test successful document parsing."""
    # Create a mock parser class that returns our mock document
    mock_parser_cls = MagicMock()
    mock_parser_instance = MagicMock()
    mock_parser_instance.parse.return_value = mock_parsed_document
    mock_parser_instance.config = {"output_format": "html"}
    mock_parser_cls.return_value = mock_parser_instance

    with patch("cneutral_doc.api.parser.service.MarkerParser", mock_parser_cls):
        # Create mock file
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = mock_pdf_file.name
        mock_file.read = AsyncMock(return_value=mock_pdf_file.read_bytes())

        # Test the endpoint
        response = await parse_document(
            request=MagicMock(),
            file=mock_file,
            isolate_tables=True,
            format="html",
            api_key=mock_api_key,
        )

        # Verify response
        assert isinstance(response, ParsedDocumentResponse)
        assert len(response.pages) == len(mock_parsed_document.pages)
        assert response.metadata == mock_parsed_document.metadata

        # Verify the parser was called correctly
        mock_parser_cls.assert_called_once_with(format="html")
        mock_parser_instance.parse.assert_called_once()


@pytest.mark.asyncio
async def test_parse_document_invalid_file(mock_api_key, tmp_path):
    """Test parsing with invalid file type."""
    # Create a mock parser class
    mock_parser_cls = MagicMock()

    with patch("cneutral_doc.api.parser.service.MarkerParser", mock_parser_cls):
        # Create a text file instead of PDF
        txt_file = tmp_path / "test.txt"
        txt_file.write_text("This is not a PDF")

        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = txt_file.name
        mock_file.read = AsyncMock(return_value=txt_file.read_bytes())

        with pytest.raises(HTTPException) as exc_info:
            await parse_document(
                request=MagicMock(),
                file=mock_file,
                isolate_tables=True,
                format="markdown",
                api_key=mock_api_key,
            )

        assert exc_info.value.status_code == HTTP_400_BAD_REQUEST
        assert "Invalid file format" in exc_info.value.detail

        # Verify the parser was not called since validation failed before parsing
        mock_parser_cls.assert_not_called()


@pytest.mark.asyncio
@pytest.mark.slow  # Mark as slow since it simulates a parsing error
async def test_parse_document_parser_error(mock_api_key, mock_pdf_file):
    """Test handling of parser exceptions."""
    # Create a mock parser that raises an exception when parse is called
    mock_parser_cls = MagicMock()
    mock_parser_instance = MagicMock()
    mock_parser_instance.parse.side_effect = Exception("Parser error")
    mock_parser_cls.return_value = mock_parser_instance

    with patch("cneutral_doc.api.parser.service.MarkerParser", mock_parser_cls):
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = mock_pdf_file.name
        mock_file.read = AsyncMock(return_value=mock_pdf_file.read_bytes())

        with pytest.raises(HTTPException) as exc_info:
            await parse_document(
                request=MagicMock(),
                file=mock_file,
                isolate_tables=True,
                format="markdown",
                api_key=mock_api_key,
            )

        assert exc_info.value.status_code == HTTP_500_INTERNAL_SERVER_ERROR
        assert "Internal server error during document parsing" in str(
            exc_info.value.detail
        )

        # Verify the parser was called
        mock_parser_cls.assert_called_once()
        mock_parser_instance.parse.assert_called_once()


@pytest.mark.asyncio
async def test_parse_document_empty_file(mock_api_key):
    """Test parsing with empty file."""
    # Create a mock parser class
    mock_parser_cls = MagicMock()

    with patch("cneutral_doc.api.parser.service.MarkerParser", mock_parser_cls):
        mock_file = MagicMock(spec=UploadFile)
        mock_file.filename = "empty.pdf"
        mock_file.read = AsyncMock(return_value=b"")

        with pytest.raises(HTTPException) as exc_info:
            await parse_document(
                request=MagicMock(),
                file=mock_file,
                isolate_tables=True,
                format="markdown",
                api_key=mock_api_key,
            )

        assert exc_info.value.status_code == HTTP_400_BAD_REQUEST
        assert "Empty file received" in exc_info.value.detail

        # Verify the parser was not called since validation failed before parsing
        mock_parser_cls.assert_not_called()
