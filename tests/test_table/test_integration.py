"""
Integration tests for the complete table filtering pipeline.

This module contains end-to-end tests that validate the entire pipeline
from document parsing through table filtering and classification.
"""

import json
import os
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

from cneutral_doc.document.parser.base import ParsedDocument, Table
from cneutral_doc.table.base import TableClassificationResult
from cneutral_doc.table.classifier import GeminiTableClassifier
from cneutral_doc.table.filter import TableFilter
from cneutral_doc.utils import get_project_root


class TestIntegration:
    """Integration tests for the complete filtering pipeline."""

    @pytest.fixture
    def project_root(self):
        """Get the project root directory."""
        from pathlib import Path

        return Path(get_project_root())

    @pytest.fixture
    def sample_esg_tables(self):
        """Create sample ESG tables for testing."""
        return [
            Table(
                index=1,
                page_number=1,
                content="""|Environmental Metric|2023|2022|2021|
|-------------------|----|----|----| 
|CO2 Emissions (tons)|1500|1600|1700|
|Energy Usage (MWh)|2500|2600|2700|
|Water Usage (m³)|3500|3600|3700|""",
            ),
            Table(
                index=2,
                page_number=2,
                content="""|Social Metric|2023|2022|2021|
|-------------|----|----|----| 
|Employees|1000|950|900|
|Training Hours|5000|4800|4600|
|Safety Incidents|5|7|10|""",
            ),
            Table(
                index=3,
                page_number=3,
                content="""|Governance Metric|2023|2022|2021|
|-----------------|----|----|----| 
|Board Meetings|12|12|11|
|Independent Directors|8|7|7|
|Audit Committee Meetings|6|6|5|""",
            ),
            Table(
                index=4,
                page_number=4,
                content="""|Table of Contents|Page|
|-----------------|----| 
|Introduction|1|
|Environmental|5|
|Social|15|
|Governance|25|""",
            ),
        ]

    @pytest.fixture
    def sample_document(self, sample_esg_tables):
        """Create a sample parsed document with ESG tables."""
        doc = MagicMock(spec=ParsedDocument)
        doc.tables = sample_esg_tables
        return doc

    @pytest.fixture
    def mock_api_key(self):
        """Provide a mock API key for testing."""
        return "test-api-key-12345"

    def test_end_to_end_filtering_pipeline(self, sample_document, mock_api_key):
        """Test the complete filtering pipeline from document to filtered results."""
        # Create a mock classifier that returns realistic results
        mock_classifier = MagicMock(spec=GeminiTableClassifier)

        # Define classification results for each table
        classification_results = [
            # Environmental table
            TableClassificationResult(
                e_score=0.9,
                s_score=0.05,
                g_score=0.05,
                other_score=0.0,
                numerical_density=0.8,
                rows=4,
                cols=4,
                is_valid_table=True,
                reasoning="Environmental metrics",
                validation_score=0.85,
            ),
            # Social table
            TableClassificationResult(
                e_score=0.1,
                s_score=0.8,
                g_score=0.1,
                other_score=0.0,
                numerical_density=0.75,
                rows=4,
                cols=4,
                is_valid_table=True,
                reasoning="Social metrics",
                validation_score=0.82,
            ),
            # Governance table
            TableClassificationResult(
                e_score=0.05,
                s_score=0.05,
                g_score=0.9,
                other_score=0.0,
                numerical_density=0.7,
                rows=4,
                cols=4,
                is_valid_table=True,
                reasoning="Governance metrics",
                validation_score=0.80,
            ),
            # Table of contents (should be filtered out)
            TableClassificationResult(
                e_score=0.0,
                s_score=0.0,
                g_score=0.0,
                other_score=1.0,
                numerical_density=0.2,
                rows=5,
                cols=2,
                is_valid_table=True,
                reasoning="Table of contents",
                validation_score=0.75,
            ),
        ]

        mock_classifier.classify_table.side_effect = classification_results

        # Create table filter with mock classifier
        table_filter = TableFilter(api_key=mock_api_key, classifier=mock_classifier)

        # Run the filtering pipeline
        result = table_filter.filter_tables(document=sample_document)

        # Verify structure
        assert "tables" in result
        assert "classifications" in result
        assert "filter_scores" in result

        # Verify all tables were processed
        assert len(result["tables"]) == 4
        assert len(result["classifications"]) == 4

        # Verify ESG categories have scores
        assert "E" in result["filter_scores"]
        assert "S" in result["filter_scores"]
        assert "G" in result["filter_scores"]

        # Verify that tables with non-zero scores are included
        # Note: Tables can have scores in multiple categories
        assert (
            len(result["filter_scores"]["E"]) >= 1
        )  # At least one environmental table
        assert len(result["filter_scores"]["S"]) >= 1  # At least one social table
        assert len(result["filter_scores"]["G"]) >= 1  # At least one governance table

        # Verify that the "Other" table (table 4) was filtered out from ESG categories
        # It should not appear in any ESG filter scores since it has other_score=1.0
        all_esg_table_ids = set()
        all_esg_table_ids.update(result["filter_scores"]["E"].keys())
        all_esg_table_ids.update(result["filter_scores"]["S"].keys())
        all_esg_table_ids.update(result["filter_scores"]["G"].keys())
        assert "4" not in all_esg_table_ids  # Table 4 (TOC) should be filtered out

    def test_top_k_selection(self, sample_document, mock_api_key):
        """Test the top-k selection functionality."""
        mock_classifier = MagicMock(spec=GeminiTableClassifier)

        # Create multiple environmental tables with different scores
        classification_results = [
            TableClassificationResult(
                e_score=0.9,
                s_score=0.05,
                g_score=0.05,
                other_score=0.0,
                numerical_density=0.8,
                rows=5,
                cols=4,
                is_valid_table=True,
                reasoning="High environmental score",
                validation_score=0.85,
            ),
            TableClassificationResult(
                e_score=0.7,
                s_score=0.15,
                g_score=0.15,
                other_score=0.0,
                numerical_density=0.6,
                rows=3,
                cols=4,
                is_valid_table=True,
                reasoning="Medium environmental score",
                validation_score=0.75,
            ),
            TableClassificationResult(
                e_score=0.5,
                s_score=0.25,
                g_score=0.25,
                other_score=0.0,
                numerical_density=0.4,
                rows=2,
                cols=4,
                is_valid_table=True,
                reasoning="Lower environmental score",
                validation_score=0.70,
            ),
            TableClassificationResult(
                e_score=0.0,
                s_score=0.0,
                g_score=0.0,
                other_score=1.0,
                numerical_density=0.2,
                rows=5,
                cols=2,
                is_valid_table=True,
                reasoning="Table of contents",
                validation_score=0.75,
            ),
        ]

        mock_classifier.classify_table.side_effect = classification_results

        table_filter = TableFilter(api_key=mock_api_key, classifier=mock_classifier)

        # Test k=1 selection
        result = table_filter.filter_tables(document=sample_document, k=1)

        # Should return top 1 table per category
        assert "E" in result
        assert "S" in result
        assert "G" in result
        assert len(result["E"]) == 1

        # The top environmental table should have the highest filter score
        top_e_table = result["E"][0]
        assert "filter_score" in top_e_table
        assert "table_id" in top_e_table
        assert "content" in top_e_table
        assert "classification" in top_e_table

    def test_validation_score_integration(self, sample_document, mock_api_key):
        """Test that validation scores are properly integrated throughout the pipeline."""
        mock_classifier = MagicMock(spec=GeminiTableClassifier)

        # Include validation scores in all results
        classification_results = [
            TableClassificationResult(
                e_score=0.8,
                s_score=0.1,
                g_score=0.1,
                other_score=0.0,
                numerical_density=0.75,
                rows=4,
                cols=4,
                is_valid_table=True,
                reasoning="Test table",
                validation_score=0.88,
            )
            for _ in range(4)
        ]

        mock_classifier.classify_table.side_effect = classification_results

        table_filter = TableFilter(api_key=mock_api_key, classifier=mock_classifier)
        result = table_filter.filter_tables(document=sample_document)

        # Verify validation scores are preserved
        for table_id, classification in result["classifications"].items():
            assert hasattr(classification, "validation_score")
            assert classification.validation_score is not None
            assert 0.0 <= classification.validation_score <= 1.0

    def test_error_handling_integration(self, sample_document, mock_api_key):
        """Test error handling in the integrated pipeline."""
        mock_classifier = MagicMock(spec=GeminiTableClassifier)

        # Mix successful and failed classifications
        classification_results = [
            TableClassificationResult(
                e_score=0.8,
                s_score=0.1,
                g_score=0.1,
                other_score=0.0,
                numerical_density=0.75,
                rows=4,
                cols=4,
                is_valid_table=True,
                reasoning="Successful classification",
                validation_score=0.85,
            ),
            Exception("Classification failed"),
            TableClassificationResult(
                e_score=0.1,
                s_score=0.8,
                g_score=0.1,
                other_score=0.0,
                numerical_density=0.7,
                rows=4,
                cols=4,
                is_valid_table=True,
                reasoning="Another successful classification",
                validation_score=0.82,
            ),
            Exception("Another classification failed"),
        ]

        mock_classifier.classify_table.side_effect = classification_results

        table_filter = TableFilter(api_key=mock_api_key, classifier=mock_classifier)
        result = table_filter.filter_tables(document=sample_document)

        # Should handle partial failures gracefully
        assert "classifications" in result
        assert len(result["classifications"]) == 4

        # Check that error tables are marked appropriately
        error_count = 0
        success_count = 0
        for table_id, classification in result["classifications"].items():
            if isinstance(classification, dict) and "error" in classification:
                error_count += 1
            else:
                success_count += 1

        assert error_count == 2  # Two failed classifications
        assert success_count == 2  # Two successful classifications

    @pytest.mark.skipif(
        not os.path.exists("tests/data/filtering/apple_all_tables.json"),
        reason="Test data file not available",
    )
    def test_real_data_integration(self, project_root, mock_api_key):
        """Test integration with real test data if available."""
        # Load real test data
        test_data_path = (
            project_root / "tests" / "data" / "filtering" / "apple_all_tables.json"
        )

        if test_data_path.exists():
            with open(test_data_path, "r") as f:
                test_data = json.load(f)

            # Create tables from test data
            tables = []
            for i, table_data in enumerate(
                test_data.get("tables", [])[:3]
            ):  # Limit to 3 for testing
                tables.append(
                    Table(
                        index=i + 1,
                        page_number=1,
                        content=table_data.get("content", ""),
                    )
                )

            if tables:
                # Create document
                doc = MagicMock(spec=ParsedDocument)
                doc.tables = tables

                # Use mock classifier to avoid API calls
                mock_classifier = MagicMock(spec=GeminiTableClassifier)
                mock_classifier.classify_table.return_value = TableClassificationResult(
                    e_score=0.6,
                    s_score=0.2,
                    g_score=0.2,
                    other_score=0.0,
                    numerical_density=0.5,
                    rows=5,
                    cols=4,
                    is_valid_table=True,
                    reasoning="Real data test",
                    validation_score=0.75,
                )

                # Test the pipeline
                table_filter = TableFilter(
                    api_key=mock_api_key, classifier=mock_classifier
                )
                result = table_filter.filter_tables(document=doc)

                # Basic validation
                assert "tables" in result
                assert "classifications" in result
                assert "filter_scores" in result
                assert len(result["tables"]) == len(tables)
