"""Tests for the table classifier module.

This module contains tests for the table classifier classes in the cneutral_doc.table.classifier module,
including LLMTableClassifier and GeminiTableClassifier.
"""

import os
from typing import Any, Dict, Optional, cast
from unittest.mock import MagicMock, patch

import langchain_core.exceptions
import pytest
from pydantic import BaseModel, ValidationError, validator
from pydantic.types import SecretStr

from cneutral_doc.table.base import BaseTableClassifier, TableClassificationResult
from cneutral_doc.table.classifier import (
    ESGScores,
    GeminiTableClassifier,
    LLMTableClassifier,
)

# Sample markdown tables for testing
VALID_TABLE = """|Year|CO2 Emissions (tons)|Energy Usage (MWh)|Water Consumption (m3)|
|----|--------------------|----------------|---------------------|
|2020|10,500              |45,000          |120,000              |
|2021|9,800               |42,500          |115,000              |
|2022|8,900               |40,000          |105,000              |
"""

INVALID_TABLE = """This is not a markdown table.
It's just some text without any table formatting.
"""

MALFORMED_TABLE = """|Column 1|Column 2
|No separator row|
|Missing cells
"""

EMPTY_TABLE = ""

# Sample ESG scores for mocking LLM responses
ENVIRONMENTAL_SCORES = {
    "reasoning": "This table contains environmental metrics like CO2 emissions, energy usage, and water consumption.",
    "E": 0.9,
    "S": 0.0,
    "G": 0.0,
    "O": 0.1,
}

SOCIAL_SCORES = {
    "reasoning": "This table contains workforce demographics and diversity metrics.",
    "E": 0.0,
    "S": 0.8,
    "G": 0.1,
    "O": 0.1,
}

GOVERNANCE_SCORES = {
    "reasoning": "This table contains board composition and executive compensation data.",
    "E": 0.0,
    "S": 0.1,
    "G": 0.9,
    "O": 0.0,
}

OTHER_SCORES = {
    "reasoning": "This appears to be a table of contents or index.",
    "E": 0.0,
    "S": 0.0,
    "G": 0.0,
    "O": 1.0,
}


# Create a concrete test implementation of LLMTableClassifier for testing
class MockLLMTableClassifier(LLMTableClassifier):
    """Concrete implementation of LLMTableClassifier for testing."""

    def _setup_chat_model(self):
        """Implement the abstract method for testing."""
        return MagicMock()


class TestTableValidation:
    """Tests for the table validation functionality."""

    def test_validate_valid_table(self):
        """Test validation of a properly formatted markdown table."""
        # Create classifier with mock API key
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

        # Test validation
        result = classifier.validate_table(VALID_TABLE)

        assert result["is_valid"] is True
        assert result["error_message"] is None
        # Updated to match new validation algorithm: 4 data rows, 4 columns (excluding empty cells)
        assert result["dimensions"] == (4, 4)
        assert 0 <= result["numerical_density"] <= 1.0
        assert "validation_score" in result
        assert 0 <= result["validation_score"] <= 1.0

    def test_validate_invalid_table(self):
        """Test validation of text that is not a markdown table."""
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

        result = classifier.validate_table(INVALID_TABLE)

        assert result["is_valid"] is False
        assert result["error_message"] is not None

    def test_validate_malformed_table(self):
        """Test validation of a malformed markdown table."""
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

        result = classifier.validate_table(MALFORMED_TABLE)

        assert result["is_valid"] is False
        assert result["error_message"] is not None

    def test_validate_empty_table(self):
        """Test validation with an empty string."""
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

        with pytest.raises(ValueError, match="Input table cannot be empty"):
            classifier.validate_table(EMPTY_TABLE)

    def test_validate_none_table(self):
        """Test validation with None input."""
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

        with pytest.raises(ValueError, match="Input table cannot be None"):
            # We're intentionally passing None here to test error handling
            # mypy will complain, but this is a valid test case
            classifier.validate_table(None)  # type: ignore

    def test_validate_non_string_table(self):
        """Test validation with non-string input."""
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )

        with pytest.raises(TypeError, match="Input table must be string"):
            # We're intentionally passing an int here to test error handling
            # mypy will complain, but this is a valid test case
            classifier.validate_table(123)  # type: ignore


class TestLLMTableClassifier:
    """Tests for the LLMTableClassifier class."""

    @pytest.fixture
    def mock_llm_classifier(self):
        """Create a mock LLMTableClassifier with a mocked chain."""
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )
        classifier.chain = MagicMock()
        return classifier

    def test_create_prompt_template(self):
        """Test that the prompt template is created correctly."""
        classifier = MockLLMTableClassifier(
            model_name="test-model", temp=0.1, api_key="test-key"
        )
        prompt_template = classifier._create_prompt_template()

        # Check that the prompt template contains the expected keys
        assert "environment_definition" in prompt_template.input_variables
        assert "social_definition" in prompt_template.input_variables
        assert "governance_definition" in prompt_template.input_variables
        assert "markdown_table" in prompt_template.input_variables

    def test_classify_table_valid(self, mock_llm_classifier):
        """Test classification of a valid table."""
        # Mock the chain to return environmental scores
        mock_llm_classifier.chain.invoke.return_value = ESGScores(
            **ENVIRONMENTAL_SCORES
        )

        # Test classification
        result = mock_llm_classifier.classify_table(VALID_TABLE)

        # Verify the result
        assert isinstance(result, TableClassificationResult)
        assert result.e_score == ENVIRONMENTAL_SCORES["E"]
        assert result.s_score == ENVIRONMENTAL_SCORES["S"]
        assert result.g_score == ENVIRONMENTAL_SCORES["G"]
        assert result.other_score == ENVIRONMENTAL_SCORES["O"]
        assert result.reasoning == ENVIRONMENTAL_SCORES["reasoning"]
        assert result.is_valid_table is True

    def test_classify_table_invalid(self, mock_llm_classifier):
        """Test classification of an invalid table."""
        result = mock_llm_classifier.classify_table(INVALID_TABLE)

        # Verify the result
        assert isinstance(result, TableClassificationResult)
        assert result.is_valid_table is False
        # The LLM should not be called for invalid tables
        mock_llm_classifier.chain.invoke.assert_not_called()

    def test_classify_table_llm_error(self, mock_llm_classifier):
        """Test handling of LLM errors during classification."""
        # Mock the chain to raise an exception
        mock_llm_classifier.chain.invoke.side_effect = (
            langchain_core.exceptions.OutputParserException("Parser error")
        )

        # Test classification with error handling
        result = mock_llm_classifier.classify_table(VALID_TABLE)

        # Verify default fallback values are used
        assert isinstance(result, TableClassificationResult)
        assert result.e_score == 0.0
        assert result.s_score == 0.0
        assert result.g_score == 0.0
        assert result.other_score == 1.0  # Default to 'Other' category
        # Updated to match new error message format
        assert result.reasoning == "Classification failed."
        assert result.validation_message is not None
        assert "Parser error" in result.validation_message

    def test_classify_table_validation_error(self, mock_llm_classifier):
        """Test handling of validation errors in ESG scores."""
        # Mock the chain to return invalid scores (don't sum to 1.0)
        invalid_scores = {
            "reasoning": "Invalid scores",
            "E": 0.3,
            "S": 0.3,
            "G": 0.3,
            "O": 0.3,  # Total is 1.2, not 1.0
        }

        # Create a simple ValidationError directly
        class TestModel(BaseModel):
            value: float

            @validator("value")
            def validate_value(cls, v):
                if v > 1.0:
                    raise ValueError("Value cannot be greater than 1.0")
                return v

        # This will raise a ValidationError when we try to validate
        try:
            TestModel(value=1.5)  # This will fail validation
            assert False, "Validation should have failed"
        except ValidationError as e:
            # Use the actual ValidationError from our test model
            mock_llm_classifier.chain.invoke.side_effect = e

        # Test classification with validation error handling
        result = mock_llm_classifier.classify_table(VALID_TABLE)

        # Verify default fallback values are used
        assert isinstance(result, TableClassificationResult)
        assert result.e_score == 0.0
        assert result.s_score == 0.0
        assert result.g_score == 0.0
        assert result.other_score == 1.0  # Default to 'Other' category
        # Updated to match new error message format
        assert result.reasoning == "Classification failed."
        assert result.validation_message is not None
        assert "validation error" in result.validation_message.lower()


# Create a concrete test implementation of GeminiTableClassifier for testing
class MockGeminiTableClassifier(GeminiTableClassifier):
    """Concrete implementation of GeminiTableClassifier for testing."""

    def _setup_chat_model(self):
        """Implement the abstract method for testing."""
        return MagicMock()


class TestGeminiTableClassifier:
    """Tests for the GeminiTableClassifier class."""

    @pytest.fixture
    def mock_gemini_classifier(self):
        """Create a mock GeminiTableClassifier with environment variables set."""
        with patch.dict(os.environ, {"GOOGLE_API_KEY": "test-env-key"}):
            classifier = MockGeminiTableClassifier()
            classifier.chain = MagicMock()
            return classifier

    def test_init_with_explicit_api_key(self):
        """Test initialization with an explicit API key."""
        classifier = MockGeminiTableClassifier(api_key="explicit-key")
        assert classifier.api_key == "explicit-key"

    def test_init_with_env_api_key(self):
        """Test initialization with an API key from environment variables."""
        with patch.dict(os.environ, {"GOOGLE_API_KEY": "env-key"}):
            # Clear any existing api_key to ensure environment variable is used
            classifier = MockGeminiTableClassifier(api_key=None)
            # The classifier should pick up the environment variable
            assert classifier.api_key == "env-key"

    def test_setup_chat_model(self):
        """Test that the chat model is set up correctly."""
        # Arrange
        # Create a mock for the ChatGoogleGenerativeAI class
        # The 'with_structured_output' method will also be part of this mock
        mock_chat_model = MagicMock()
        mock_chat_model_instance = MagicMock()
        mock_chat_model.return_value = mock_chat_model_instance

        # Patch ChatGoogleGenerativeAI where it's imported/used
        with patch(
            "cneutral_doc.table.classifier.ChatGoogleGenerativeAI", mock_chat_model
        ) as mock_chat_class:
            # Act: Initialize the classifier, which calls _setup_chat_model
            classifier = GeminiTableClassifier(api_key="test_api_key")

            # Assert: Check that ChatGoogleGenerativeAI was instantiated correctly
            mock_chat_class.assert_called_once()
            _, kwargs = mock_chat_class.call_args
            assert kwargs.get("model") == "gemini-2.0-flash"  # Correct default model
            assert kwargs.get("temperature") == 0.1  # Default temp
            assert isinstance(kwargs.get("api_key"), SecretStr)
            assert kwargs.get("api_key").get_secret_value() == "test_api_key"

            # Assert: Check that with_structured_output was called on the model instance
            mock_chat_model_instance.with_structured_output.assert_called_once_with(
                ESGScores
            )

            # Assert: Check that the chain is properly constructed
            # The chain should have 2 steps: prompt template and model
            assert len(classifier.chain.steps) == 2
            # First step should be the prompt template
            assert isinstance(classifier.chain.steps[0].__class__.__name__, str)
            assert "ChatPromptTemplate" in classifier.chain.steps[0].__class__.__name__
            # We've already verified the model setup with the previous assertions

    def test_classify_table(self, mock_gemini_classifier):
        """Test classification using the Gemini model."""
        # Mock the chain to return governance scores
        mock_gemini_classifier.chain.invoke.return_value = ESGScores(
            **GOVERNANCE_SCORES
        )

        # Test classification
        result = mock_gemini_classifier.classify_table(VALID_TABLE)

        # Verify the result
        assert isinstance(result, TableClassificationResult)
        assert result.e_score == GOVERNANCE_SCORES["E"]
        assert result.s_score == GOVERNANCE_SCORES["S"]
        assert result.g_score == GOVERNANCE_SCORES["G"]
        assert result.other_score == GOVERNANCE_SCORES["O"]
        assert result.reasoning == GOVERNANCE_SCORES["reasoning"]
        assert result.is_valid_table is True


if __name__ == "__main__":
    pytest.main()
