import logging
from pathlib import Path
from typing import Literal, Optional, Union, cast
from unittest.mock import MagicMock, patch

import pytest
from langchain.schema import AIMessage
from pydantic import BaseModel, Field, ValidationError

# Import the original TaggingOutput but rename it
from cneutral_doc.document.tagger import (
    DOCUMENT_TYPES,
    DocumentProcessingError,
    DocumentSizeError,
    DocumentTaggingError,
    LLMDocTagger,
    ModelInferenceError,
    OpenAIDocTagger,
)
from cneutral_doc.document.tagger import TaggingOutput as OriginalTaggingOutput


# Create a test-specific version of TaggingOutput
class TaggingOutput(BaseModel):
    """Test-specific version of TaggingOutput that can handle string values."""

    company_name: Optional[str] = Field(
        default=None,
        description="Official full English name of the company from Wikipedia, e.g., Shell plc instead of Shell",
    )
    document_title: Optional[str] = Field(
        default=None, description="Official title of the document provided"
    )
    language: Optional[str] = Field(
        default=None,
        description="Main language of the document using ISO 639-1 two-letter code (e.g., 'en' for English, 'es' for Spanish, 'zh' for Chinese)",
    )
    publication_year: Optional[str] = Field(
        default=None, description="Year of publication in 4 digits, e.g., '2021'"
    )
    doc_type: str = Field(
        description=(
            "Inferred document type based on the title, the table of contents and other contents provided:\n"
            "- annual_report: Annual financial reports covering a full year\n"
            "- esg_report: ESG, impact, sustainability reports\n"
            "- integrated_report: Combined annual and ESG reports\n"
            "- other: Other document types"
        )
    )


# Define typed constants for document types
DOC_TYPE_ANNUAL_REPORT = cast(Literal["annual_report"], "annual_report")
DOC_TYPE_ESG_REPORT = cast(Literal["esg_report"], "esg_report")
DOC_TYPE_INTEGRATED_REPORT = cast(Literal["integrated_report"], "integrated_report")
DOC_TYPE_OTHER = cast(Literal["other"], "other")

# Test data
SAMPLE_TEXT = """Shell plc
Annual Report and Accounts 2023
Table of Contents
1. Strategic Report
2. Financial Statements"""

VALID_METADATA = {
    "company_name": "Shell plc",
    "document_title": "Annual Report and Accounts 2023",
    "language": "en",
    "publication_year": "2023",
    "doc_type": "annual_report",
}


@pytest.fixture
def sample_pdf_path(tmp_path):
    """Create a temporary PDF file for testing."""
    pdf_path = tmp_path / "test.pdf"
    pdf_path.write_text("dummy content")
    return str(pdf_path)


@pytest.fixture
def mock_pdf_content():
    """Mock PDF content for testing."""
    return SAMPLE_TEXT


class TestTaggingOutput:
    def test_valid_output(self):
        """Test TaggingOutput with valid data."""
        output = TaggingOutput(**VALID_METADATA)
        assert output.company_name == "Shell plc"
        assert output.doc_type == "annual_report"

    def test_optional_fields(self):
        """Test TaggingOutput with minimal required fields."""
        minimal_data = {"doc_type": "other"}
        output = TaggingOutput(**minimal_data)
        assert output.company_name is None
        assert output.document_title is None
        assert output.doc_type == "other"


class TestLLMDocTagger:
    @pytest.fixture
    def mock_llm_tagger(self):
        """Create a concrete LLMDocTagger for testing."""

        class ConcreteLLMDocTagger(LLMDocTagger):
            def _get_llm_chain(self):
                return MagicMock()

        return ConcreteLLMDocTagger()

    def test_extract_initial_pages(
        self, mock_llm_tagger, sample_pdf_path, mock_pdf_content
    ):
        """Test extracting initial pages from PDF."""
        with patch("pdfplumber.open") as mock_pdf:
            mock_page = MagicMock()
            mock_page.extract_text.return_value = mock_pdf_content
            mock_pdf.return_value.__enter__.return_value.pages = [mock_page] * 3

            result = mock_llm_tagger._extract_initial_pages(sample_pdf_path)
            assert mock_pdf_content in result
            assert mock_page.extract_text.call_count == 3

    def test_extract_initial_pages_file_not_found(self, mock_llm_tagger):
        """Test handling of non-existent PDF file."""
        with pytest.raises(DocumentProcessingError):
            mock_llm_tagger._extract_initial_pages("nonexistent.pdf")

    def test_extract_initial_pages_empty_content(
        self, mock_llm_tagger, sample_pdf_path
    ):
        """Test handling of PDF with no extractable text."""
        with patch("pdfplumber.open") as mock_pdf:
            mock_page = MagicMock()
            mock_page.extract_text.return_value = ""
            mock_pdf.return_value.__enter__.return_value.pages = [mock_page] * 3

            result = mock_llm_tagger._extract_initial_pages(sample_pdf_path)
            assert result == ""

    def test_validate_file_size_within_limit(self, mock_llm_tagger, tmp_path):
        """Test file size validation with file within size limit."""
        test_file = tmp_path / "small_file.pdf"
        test_file.write_text("small content")  # Create a small file

        # Mock stat to return size under limit
        with patch.object(Path, "stat") as mock_stat:
            mock_stat.return_value.st_size = 1024 * 1024  # 1MB
            # Should not raise exception
            mock_llm_tagger._validate_file_size(test_file)

    def test_validate_file_size_exceeds_limit(self, mock_llm_tagger, tmp_path):
        """Test file size validation with file exceeding size limit."""
        test_file = tmp_path / "large_file.pdf"
        test_file.write_text("large content")

        # Mock stat to return size over limit
        with patch.object(Path, "stat") as mock_stat:
            mock_stat.return_value.st_size = 1024 * 1024 * 1000  # 1000MB
            with pytest.raises(DocumentSizeError) as exc_info:
                mock_llm_tagger._validate_file_size(test_file)

            assert "exceeds maximum allowed size" in str(exc_info.value)

    def test_extract_initial_pages_with_large_file(self, mock_llm_tagger, tmp_path):
        """Test _extract_initial_pages with file exceeding size limit."""
        large_file = tmp_path / "large_file.pdf"
        large_file.write_text("large content")

        with patch.object(Path, "stat") as mock_stat:
            mock_stat.return_value.st_size = 1024 * 1024 * 1000  # 1000MB

            with pytest.raises(DocumentSizeError) as exc_info:
                mock_llm_tagger._extract_initial_pages(str(large_file))

            assert "exceeds maximum allowed size" in str(exc_info.value)


class TestOpenAIDocTagger:
    @pytest.fixture
    def openai_tagger(self):
        return OpenAIDocTagger(model_name="test-model")

    def test_initialization(self, openai_tagger):
        """Test OpenAIDocTagger initialization."""
        assert openai_tagger.model_name == "test-model"
        assert openai_tagger.temperature == 0

    @patch(
        "cneutral_doc.document.tagger.ChatOpenAI"
    )  # Updated patch path for new module structure
    def test_get_llm_chain(self, mock_chat_openai, openai_tagger):
        """Test LLM chain creation."""
        # Setup mock
        mock_chain = MagicMock()
        mock_chat_openai.return_value = MagicMock()
        mock_chat_openai.return_value.with_structured_output.return_value = mock_chain

        # Execute
        chain = openai_tagger._get_llm_chain()

        # Assert
        assert chain is not None
        mock_chat_openai.assert_called_once_with(model="test-model", temperature=0)
        mock_chat_openai.return_value.with_structured_output.assert_called_once_with(
            OriginalTaggingOutput, method="function_calling"
        )

    @patch(
        "cneutral_doc.document.tagger.ChatOpenAI"
    )  # Updated patch path for new module structure
    def test_tag_text_success(self, mock_chat_openai, openai_tagger):
        """Test successful text tagging."""
        mock_chain = MagicMock()
        mock_chain.invoke.return_value = TaggingOutput(**VALID_METADATA)
        openai_tagger._chain = mock_chain

        result = openai_tagger.tag_text(SAMPLE_TEXT)
        assert result["company_name"] == "Shell plc"
        assert result["doc_type"] == "annual_report"
        mock_chain.invoke.assert_called_once()

    @patch(
        "cneutral_doc.document.tagger.ChatOpenAI"
    )  # Updated patch path for new module structure
    def test_tag_document_success(
        self, mock_chat_openai, openai_tagger, sample_pdf_path
    ):
        """Test successful document tagging."""
        with patch.object(openai_tagger, "_extract_initial_pages") as mock_extract:
            mock_extract.return_value = SAMPLE_TEXT
            mock_chain = MagicMock()
            mock_chain.invoke.return_value = TaggingOutput(**VALID_METADATA)
            openai_tagger._chain = mock_chain

            result = openai_tagger.tag_document(sample_pdf_path)
            assert result["company_name"] == "Shell plc"
            assert result["doc_type"] == "annual_report"
            mock_extract.assert_called_once_with(sample_pdf_path)

    def test_tag_document_no_content(self, openai_tagger, sample_pdf_path):
        """Test document tagging with no extractable content."""
        with patch.object(openai_tagger, "_extract_initial_pages") as mock_extract:
            mock_extract.return_value = ""
            with pytest.raises(DocumentProcessingError):
                openai_tagger.tag_document(sample_pdf_path)

    def test_tag_document_with_large_file(self, openai_tagger, tmp_path):
        """Test tag_document with file exceeding size limit."""
        large_file = tmp_path / "large_file.pdf"
        large_file.write_text("large content")

        with patch.object(Path, "stat") as mock_stat:
            mock_stat.return_value.st_size = 1024 * 1024 * 1000  # 1000MB

            with pytest.raises(DocumentSizeError) as exc_info:
                openai_tagger.tag_document(str(large_file))

            assert "exceeds maximum allowed size" in str(exc_info.value)
