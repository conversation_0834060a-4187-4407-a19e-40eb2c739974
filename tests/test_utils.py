import os
from pathlib import Path
from unittest.mock import mock_open, patch

import pytest
import yaml

from cneutral_doc.utils import get_data_root, get_project_root, read_config

# Test data
MOCK_CONFIG = {"paths": {"data_root": "/mock/data/path"}}


@pytest.fixture
def mock_config_file():
    return yaml.dump(MOCK_CONFIG)


def test_get_project_root():
    """Test get_project_root returns a valid path string"""
    root_path = get_project_root()
    assert isinstance(root_path, str)
    assert len(root_path) > 0
    # Verify it's an absolute path
    assert Path(root_path).is_absolute()


@pytest.mark.parametrize(
    "config_path,config_dir",
    [
        ("config.yaml", "config"),
        ("custom_config.yaml", "custom_dir"),
    ],
)
def test_read_config(mock_config_file, config_path, config_dir):
    """Test read_config with different paths and mock file content"""
    mock_path = Path("/mock/path")

    with (
        patch("cneutral_doc.utils.get_project_root", return_value=str(mock_path)),
        patch("builtins.open", mock_open(read_data=mock_config_file)),
    ):

        config = read_config(config_path=config_path, config_dir=config_dir)

        assert isinstance(config, dict)
        assert config == MOCK_CONFIG
        assert "paths" in config
        assert "data_root" in config["paths"]


def test_read_config_file_not_found():
    """Test read_config raises error for non-existent file"""
    with pytest.raises(FileNotFoundError):
        read_config(config_path="nonexistent.yaml")


def test_get_data_root(mock_config_file):
    """Test get_data_root returns correct path from config"""
    with patch("cneutral_doc.utils.read_config", return_value=MOCK_CONFIG):
        data_root = get_data_root()
        assert data_root == MOCK_CONFIG["paths"]["data_root"]


def test_get_data_root_missing_key():
    """Test get_data_root raises error when config is missing required keys"""
    with patch("cneutral_doc.utils.read_config", return_value={}):
        with pytest.raises(KeyError):
            get_data_root()
