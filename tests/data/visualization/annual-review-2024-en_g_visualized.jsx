function GPlot() {
  const data = [
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [6498, 6739],
      name: 'Total sales',
      marker: { color: 'rgb(31, 119, 180)' }
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [777, 943],
      name: 'Underlying trading operating profit',
      marker: { color: 'rgb(255, 127, 14)' }
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [670, 794],
      name: 'Trading operating profit',
      marker: { color: 'rgb(44, 160, 44)' }
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [478, 522],
      name: 'Capital additions',
      marker: { color: 'rgb(214, 39, 40)' }
    }
  ];

  const layout = {
    title: 'Financial Performance 2023 vs 2024',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Value' },
    barmode: 'group',
    responsive: true
  };

  const percentageData = [
    {
      type: 'bar',
      x: ['Total Sales', 'Underlying Trading Operating Profit', 'Trading Operating Profit', 'Capital Additions'],
      y: [5.5, 14.0, 11.8, 7.7],
      name: 'Percentage Change (%)',
      marker: { color: 'rgb(148, 103, 189)' }
    }
  ];

  const percentageLayout = {
    title: 'Percentage Change from 2023 to 2024',
    xaxis: { title: 'Metric' },
    yaxis: { title: 'Percentage Change (%)' },
    responsive: true
  };

  return (
    <div>
      <Plot data={data} layout={layout} />
      <Plot data={percentageData} layout={percentageLayout} />
    </div>
  );
}
