function SPlot() {
  const data = [
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [5037, 4973],
      name: 'Total Sales',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [936, 947],
      name: '19.0%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [46, 38],
      name: '0.8%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [1080, 1017],
      name: '20.5%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [902, 876],
      name: '17.6%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [800, 801],
      name: '16.1%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [128, 126],
      name: '2.5%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [1145, 1168],
      name: '23.5%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [832, 803],
      name: '16.1% (duplicate)',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [468, 770],
      name: '15.5%',
    },
    {
      type: 'bar',
      x: ['2023', '2024'],
      y: [161, 133],
      name: '2.7%',
    },
  ];

  const layout = {
    title: 'Sales Comparison Between 2023 and 2024',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Sales' },
    barmode: 'group',
    responsive: true,
  };

  return (
    <Plot
      data={data}
      layout={layout}
      style={{ width: '100%', height: '500px' }}
    />
  );
}
