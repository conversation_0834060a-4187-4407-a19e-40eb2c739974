 function EPlot() {
  const electricityData = [
   {
    building: "Guoco Tower",
    FY2019: 14688394,
    FY2021: 13666629,
   },
   {
    building: "Guoco Midtown Office",
    FY2023: 2743946,
   },
   {
    building: "20 Collyer Quay",
    FY2019: 4023271,
    FY2020: 3879054,
    FY2021: 3552523,
    FY2022: 3568773,
    FY2023: 3628518,
   },
   {
    building: "Sofitel Singapore City Centre",
    FY2019: 5162187,
    FY2020: 4795747,
    FY2021: 4371120,
    FY2022: 4861321,
    FY2023: 5076503,
   },
   {
    building: "Guoco Changfeng City",
    FY2023: 1623606,
   },
  ];
 

  const naturalGasData = [
   {
    building: "Sofitel Singapore City Centre",
    FY2023: 352979,
   },
   {
    building: "Guoco Changfeng City",
    FY2023: 656691,
   },
  ];
 

  const waterData = [
   {
    building: "Guoco Tower",
    FY2019: 165425,
    FY2020: 138888,
    FY2021: 94837,
    FY2022: 100404,
    FY2023: 113308,
   },
   {
    building: "Guoco Midtown Office",
    FY2023: 26234,
   },
   {
    building: "20 Collyer Quay",
    FY2019: 33215,
    FY2020: 36160,
    FY2021: 25692,
    FY2022: 21146,
    FY2023: 24686,
   },
   {
    building: "Sofitel Singapore City Centre",
    FY2019: 72305,
    FY2020: 80857,
    FY2021: 33903,
    FY2022: 43168,
    FY2023: 51427,
   },
   {
    building: "Guoco Changfeng City",
    FY2023: 10166,
   },
  ];
 

  const electricityTraces = Object.keys(electricityData[0])
   .filter((key) => key !== "building")
   .map((year) => ({
    x: electricityData.map((item) => item.building),
    y: electricityData.map((item) => item[year] || 0),
    type: "bar",
    name: year,
   }));
 

  const naturalGasTraces = Object.keys(naturalGasData[0])
   .filter((key) => key !== "building")
   .map((year) => ({
    x: naturalGasData.map((item) => item.building),
    y: naturalGasData.map((item) => item[year] || 0),
    type: "bar",
    name: year,
   }));
 

  const waterTraces = Object.keys(waterData[0])
   .filter((key) => key !== "building")
   .map((year) => ({
    x: waterData.map((item) => item.building),
    y: waterData.map((item) => item[year] || 0),
    type: "bar",
    name: year,
   }));
 

  const totalEnergyConsumptionData = [
   { year: "FY2020", value: 23873852 },
   { year: "FY2021", value: 22666358 },
   { year: "FY2021", value: 21210186 },
   { year: "FY2021", value: 22096723 },
   { year: "FY2023", value: 26857805 },
  ];
 

  const totalEnergyConsumptionTrace = {
   x: totalEnergyConsumptionData.map((item) => item.year),
   y: totalEnergyConsumptionData.map((item) => item.value),
   type: "scatter",
   mode: "lines+markers",
   name: "Total Energy Consumption",
  };
 

  const totalWaterConsumptionData = [
   { year: "FY2019", value: 270945 },
   { year: "FY2020", value: 255905 },
   { year: "FY2021", value: 154432 },
   { year: "FY2022", value: 164717 },
   { year: "FY2023", value: 225820 },
  ];
 

  const totalWaterConsumptionTrace = {
   x: totalWaterConsumptionData.map((item) => item.year),
   y: totalWaterConsumptionData.map((item) => item.value),
   type: "scatter",
   mode: "lines+markers",
   name: "Total Water Consumption",
  };
 

  const electricityLayout = {
   title: "Electricity Consumption (kWh) by Building",
   xaxis: { title: "Building" },
   yaxis: { title: "Consumption (kWh)" },
   barmode: "group",
   responsive: true,
  };
 

  const naturalGasLayout = {
   title: "Natural Gas Consumption (kWh) by Building",
   xaxis: { title: "Building" },
   yaxis: { title: "Consumption (kWh)" },
   barmode: "group",
   responsive: true,
  };
 

  const waterLayout = {
   title: "Water Consumption (m3) by Building",
   xaxis: { title: "Building" },
   yaxis: { title: "Consumption (m3)" },
   barmode: "group",
   responsive: true,
  };
 

  const totalEnergyLayout = {
   title: "Total Energy Consumption",
   xaxis: { title: "Year" },
   yaxis: { title: "Consumption" },
   responsive: true,
  };
 

  const totalWaterLayout = {
   title: "Total Water Consumption",
   xaxis: { title: "Year" },
   yaxis: { title: "Consumption" },
   responsive: true,
  };
 

  return (
   <div>
    <div>
     <Plot
      data={electricityTraces}
      layout={electricityLayout}
      style={{ width: "100%", height: "400px" }}
     />
    </div>
    <div>
     <Plot
      data={naturalGasTraces}
      layout={naturalGasLayout}
      style={{ width: "100%", height: "400px" }}
     />
    </div>
    <div>
     <Plot
      data={waterTraces}
      layout={waterLayout}
      style={{ width: "100%", height: "400px" }}
     />
    </div>
    <div>
     <Plot
      data={[totalEnergyConsumptionTrace]}
      layout={totalEnergyLayout}
      style={{ width: "100%", height: "400px" }}
     />
    </div>
    <div>
     <Plot
      data={[totalWaterConsumptionTrace]}
      layout={totalWaterLayout}
      style={{ width: "100%", height: "400px" }}
     />
    </div>
   </div>
  );
 }
