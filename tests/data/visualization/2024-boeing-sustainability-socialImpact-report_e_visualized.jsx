function EPlot() {
  const data = [
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [745, 661, 590],
      name: 'Hazardous waste incinerated for energy recovery',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [1118, 701, 843],
      name: 'Hazardous waste incinerated without energy recovery',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [10, 6, 61],
      name: 'Hazardous waste recycled',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [6295, 7270, 6061],
      name: 'Hazardous waste directed to disposal',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [2686, 2473, 1977],
      name: 'Hazardous waste sent to landfill',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [1747, 3435, 2651],
      name: 'Hazardous waste otherwise disposed',
    },
  ];

  const layout = {
    title: 'Hazardous Waste Data',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Tonnes' },
    barmode: 'group',
    responsive: true,
  };

  const data2 = [
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [64, 155, 286],
      name: 'Nonhazardous waste incinerated for energy recovery',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [141, 81, 365],
      name: 'Nonhazardous waste incinerated without energy recovery',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [50, 39, 43],
      name: 'Nonhazardous waste recycled',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [8528, 7726, 11938],
      name: 'Nonhazardous waste directed to disposal',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [291, 151, 149],
      name: 'Nonhazardous waste sent to landfill',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [8032, 7339, 11138],
      name: 'Nonhazardous waste otherwise disposed',
    },
  ];

  const layout2 = {
    title: 'Nonhazardous Waste Data',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Tonnes' },
    barmode: 'group',
    responsive: true,
  };

  const data3 = [
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [0, 0, 4],
      name: 'Universal waste incinerated for energy recovery',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [29, 15, 2],
      name: 'Universal waste incinerated without energy recovery',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [32, 22, 16],
      name: 'Universal waste recycled',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [375, 908, 138],
      name: 'Universal waste directed to disposal',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [15, 13, 11],
      name: 'Universal waste sent to landfill',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021'],
      y: [331, 880, 123],
      name: 'Universal waste otherwise disposed',
    },
  ];

  const layout3 = {
    title: 'Universal Waste Data',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Tonnes' },
    barmode: 'group',
    responsive: true,
  };

  const data4 = [
    {
      type: 'scatter',
      x: ['2023', '2022', '2021'],
      y: [0.2, 0.1, 1.0],
      name: 'Percentage of hazardous waste recycled',
    },
  ];

  const layout4 = {
    title: 'Percentage of Hazardous Waste Recycled',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Percentage' },
    responsive: true,
  };

  const data5 = [
    {
      type: 'scatter',
      x: ['2023', '2022', '2021'],
      y: [0.6, 0.5, 0.4],
      name: 'Percentage of nonhazardous waste recycled',
    },
  ];

  const layout5 = {
    title: 'Percentage of Nonhazardous Waste Recycled',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Percentage' },
    responsive: true,
  };

  const data6 = [
    {
      type: 'scatter',
      x: ['2023', '2022', '2021'],
      y: [8, 2, 10],
      name: 'Percentage of universal waste recycled',
    },
  ];

  const layout6 = {
    title: 'Percentage of Universal Waste Recycled',
    xaxis: { title: 'Year' },
    yaxis: { title: 'Percentage' },
    responsive: true,
  };

  return (
    <div>
      <Plot data={data} layout={layout} />
      <Plot data={data2} layout={layout2} />
      <Plot data={data3} layout={layout3} />
      <Plot data={data4} layout={layout4} />
      <Plot data={data5} layout={layout5} />
      <Plot data={data6} layout={layout6} />
    </div>
  );
}
