function plot() {
  const data1 = [
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [324100, 324000, 166380, 334430, 573730],
      name: 'Gross emissions',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [55200, 55200, 55200, 47430, 52730],
      name: 'Scope 1',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [35300, 39700, 40070, 39340, 40910],
      name: 'Natural gas, diesel, propane',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [17000, 12600, 12090, 4270, 6950],
      name: 'Fleet vehicles',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [2900, 2900, 3040, 3830, 4870],
      name: 'Other emissions',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [3400, 3000, 2780, 0, 0],
      name: 'Scope 2 (market-based)',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [0, 0, 0, 0, 0],
      name: 'Electricity',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [3400, 3000, 2780, 0, 0],
      name: 'Steam, heating, and cooling',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [412800, 265800, 108400, 287000, 521000],
      name: 'Scope 3',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [225700, 113500, 22850, 153000, 326000],
      name: 'Business travel',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [164100, 134200, 85570, 134000, 195000],
      name: 'Employee commute',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [18300, 10600, 0, 0, 0],
      name: 'Upstream impacts (scope 1)',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [4700, 7500, 0, 0, 0],
      name: 'Work from home (market-based)',
    },
  ];

  const layout1 = {
    title: 'Corporate Emissions (metric tons CO2e)',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'metric tons CO2e' },
    barmode: 'stack',
    width: 900,
    height: 600,
  };

  const data2 = [
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [-471400, -324100, -167000, -70000, 0],
      name: 'Corporate carbon offsets',
    },
  ];

  const layout2 = {
    title: 'Corporate Carbon Offsets (metric tons CO2e)',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'metric tons CO2e' },
    width: 900,
    height: 500,
  };

  const data3 = [
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [15570000, 20280000, 23020000, 22260000, 24460000],
      name: 'Gross emissions (Scope 3)',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [9400000, 13400000, 16200000, 16100000, 18900000],
      name: 'Manufacturing (purchased goods and services)',
    },
    {
      type: 'bar',
      x: ['2022', '2021', '2020', '2019'],
      y: [1900000, 1750000, 1800000, 1400000],
      name: 'Product transportation (upstream and downstream) 1,500,000',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [4600000, 4900000, 4990000, 4300000, 4100000],
      name: 'Product use (use of sold products)',
    },
    {
      type: 'bar',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [70000, 80000, 80000, 60000, 60000],
      name: 'End-of-life processing',
    },
  ];

  const layout3 = {
    title: 'Product Life Cycle Emissions (metric tons CO2e)',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'metric tons CO2e' },
    barmode: 'stack',
    width: 900,
    height: 600,
  };

  const data4 = [
    {
      type: 'bar',
      x: ['2023', '2021'],
      y: [-13500, -500000],
      name: 'Product carbon offsets',
    },
  ];

  const layout4 = {
    title: 'Product Carbon Offsets (metric tons CO2e)',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'metric tons CO2e' },
    width: 900,
    height: 500,
  };

  const data5 = [
    {
      type: 'scatter',
      mode: 'lines+markers',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [15980000, 20545800, 23128400, 22550000, 24980000],
      name: 'Total gross scope 3 emissions (corporate and product)',
    },
  ];

  const layout5 = {
    title: 'Total Gross Scope 3 Emissions (corporate and product) (metric tons CO2e)',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'metric tons CO2e' },
    width: 900,
    height: 500,
  };

  const data6 = [
    {
      type: 'scatter',
      mode: 'lines+markers',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [16100000, 20600000, 23200000, 22600000, 25100000],
      name: 'Total gross carbon footprint (without offsets)',
    },
  ];

  const layout6 = {
    title: 'Total Gross Carbon Footprint (without offsets) (metric tons CO2e)',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'metric tons CO2e' },
    width: 900,
    height: 500,
  };

  const data7 = [
    {
      type: 'scatter',
      mode: 'lines+markers',
      x: ['2023', '2022', '2021', '2020', '2019'],
      y: [15600000, 20300000, 22530000, 22530000, 25100000],
      name: 'Total net carbon footprint (after applying offsets)',
    },
  ];

  const layout7 = {
    title: 'Total Net Carbon Footprint (after applying offsets) (metric tons CO2e)',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'metric tons CO2e' },
    width: 900,
    height: 500,
  };

  return React.createElement(
    'div',
    null,
    React.createElement(Plotly.react, 'graph1', data1, layout1, { responsive: true }),
    React.createElement(Plotly.react, 'graph2', data2, layout2, { responsive: true }),
    React.createElement(Plotly.react, 'graph3', data3, layout3, { responsive: true }),
    React.createElement(Plotly.react, 'graph4', data4, layout4, { responsive: true }),
    React.createElement(Plotly.react, 'graph5', data5, layout5, { responsive: true }),
    React.createElement(Plotly.react, 'graph6', data6, layout6, { responsive: true }),
    React.createElement(Plotly.react, 'graph7', data7, layout7, { responsive: true })
  );
}
