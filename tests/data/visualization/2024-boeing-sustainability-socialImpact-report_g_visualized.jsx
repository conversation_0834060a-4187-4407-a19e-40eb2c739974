const GPlot = () => {
  const femaleRepresentationData = [
    {
      x: ["2023", "2022", "2021"],
      y: [24.4, 24.1, 23.6],
      type: "bar",
      name: "Overall (companywide)",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [30.8, 30.8, 25.0],
      type: "bar",
      name: "Board of Directors",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [25.0, 19.0, 19.0],
      type: "bar",
      name: "Executive Council",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [33.3, 33.2, 33.5],
      type: "bar",
      name: "Executives",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [24.3, 23.7, 22.9],
      type: "bar",
      name: "Managers",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [24.5, 25.3, 24.7],
      type: "bar",
      name: "New hires",
    },
  ];

  const femaleRepresentationLayout = {
    title: "Female Representation",
    xaxis: { title: "Year" },
    yaxis: { title: "Percentage" },
    barmode: "group",
    responsive: true,
  };

  const womenOfColorData = [
    {
      x: ["2023", "2022", "2021"],
      y: [10.6, 9.9, 9.1],
      type: "bar",
      name: "Percentage of women of color",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [8.7, 8.0, 8.4],
      type: "bar",
      name: "Percentage of women of color among U.S. executives",
    },
  ];

  const womenOfColorLayout = {
    title: "Representation of Women of Color",
    xaxis: { title: "Year" },
    yaxis: { title: "Percentage" },
    barmode: "group",
    responsive: true,
  };

  const racialEthnicData = [
    {
      x: ["2023", "2022", "2021"],
      y: [37.6, 35.3, 32.7],
      type: "bar",
      name: "Overall (companywide)",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [25.0, 25.0, 25.0],
      type: "bar",
      name: "Board of Directors",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [17.6, 21.1, 33.3],
      type: "bar",
      name: "Executive Council",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [22.6, 21.8, 22.5],
      type: "bar",
      name: "Executives",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [28.7, 27.1, 24.3],
      type: "bar",
      name: "Managers",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [48.6, 47.5, 42.5],
      type: "bar",
      name: "New hires (%)",
    },
  ];

  const racialEthnicLayout = {
    title: "Racial and Ethnic Minority Representation",
    xaxis: { title: "Year" },
    yaxis: { title: "Percentage" },
    barmode: "group",
    responsive: true,
  };

  const workforceCompositionData = [
    {
      x: ["2023", "2022", "2021"],
      y: [0.8, 0.7, 0.7],
      type: "bar",
      name: "Pacific Islander",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [0.8, 0.8, 0.8],
      type: "bar",
      name: "Native American",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [61.8, 64.2, 67.1],
      type: "bar",
      name: "White",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [7.5, 7.1, 6.6],
      type: "bar",
      name: "Black",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [2.9, 2.6, 2.3],
      type: "bar",
      name: "2 or more races",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [16.5, 15.9, 14.6],
      type: "bar",
      name: "Asian",
    },
    {
      x: ["2023", "2022", "2021"],
      y: [9.2, 8.1, 7.4],
      type: "bar",
      name: "Hispanic",
    },
  ];

  const workforceCompositionLayout = {
    title: "Workforce Composition",
    xaxis: { title: "Year" },
    yaxis: { title: "Percentage" },
    barmode: "group",
    responsive: true,
  };

  const newHiresData = [
    {
      x: ["2023", "2022", "2021"],
      y: [9826, 9510, 3585],
      type: "bar",
      name: "New hires (#)",
    },
  ];

  const newHiresLayout = {
    title: "New Hires",
    xaxis: { title: "Year" },
    yaxis: { title: "Number of Hires" },
    responsive: true,
  };

  return (
    <div>
      <Plot data={femaleRepresentationData} layout={femaleRepresentationLayout} />
      <Plot data={womenOfColorData} layout={womenOfColorLayout} />
      <Plot data={racialEthnicData} layout={racialEthnicLayout} />
      <Plot data={workforceCompositionData} layout={workforceCompositionLayout} />
      <Plot data={newHiresData} layout={newHiresLayout} />
    </div>
  );
};
