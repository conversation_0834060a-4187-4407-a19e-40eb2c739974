function plot() {
  const netSalesData = [
    { year: '2023', value: 383285 },
    { year: '2022', value: 394328 },
    { year: '2021', value: 365817 },
    { year: '2020', value: 274515 },
    { year: '2019', value: 260174 },
  ];

  const employeeData = [
    { year: '2023', value: 161000 },
    { year: '2022', value: 164000 },
    { year: '2021', value: 154000 },
    { year: '2020', value: 147000 },
    { year: '2019', value: 137000 },
  ];

  const netSalesTraces = [{
    x: netSalesData.map(item => item.year),
    y: netSalesData.map(item => item.value),
    type: 'bar',
    name: 'Net Sales (in millions, US$)',
  }];

  const employeeTraces = [{
    x: employeeData.map(item => item.year),
    y: employeeData.map(item => item.value),
    type: 'scatter',
    mode: 'lines+markers',
    name: 'Number of Full-Time Equivalent Employees',
  }];

  const netSalesLayout = {
    title: 'Net Sales Over Years',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'Net Sales (millions, US$)' },
    responsive: true
  };

  const employeeLayout = {
    title: 'Number of Employees Over Years',
    xaxis: { title: 'Fiscal Year' },
    yaxis: { title: 'Number of Employees' },
    responsive: true
  };

  return React.createElement('div', null, [
    React.createElement(Plotly.react, {key: 'net-sales', data: netSalesTraces, layout: netSalesLayout, config: {responsive: true}}),
    React.createElement(Plotly.react, {key: 'employees', data: employeeTraces, layout: employeeLayout, config: {responsive: true}})
  ]);
}
