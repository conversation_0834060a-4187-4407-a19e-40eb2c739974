"""Global pytest configuration for the cneutral-doc test suite."""

import os
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

from cneutral_doc.document.parser.base import Page, ParsedDocument, Table


def pytest_configure(config):
    """Configure pytest with custom markers."""
    config.addinivalue_line("markers", "gpu: mark test as requiring GPU resources")
    config.addinivalue_line("markers", "slow: mark test as slow running")


def pytest_runtest_setup(item):
    """Skip tests based on environment variables and markers."""
    # Skip GPU tests if SKIP_GPU_TESTS is set
    if "gpu" in item.keywords and os.environ.get("SKIP_GPU_TESTS"):
        pytest.skip("Skipping GPU test due to SKIP_GPU_TESTS environment variable")

    # Skip slow tests if SKIP_SLOW_TESTS is set
    if "slow" in item.keywords and os.environ.get("SKIP_SLOW_TESTS"):
        pytest.skip("Skipping slow test due to SKIP_SLOW_TESTS environment variable")


@pytest.fixture(autouse=True)
def disable_gpu():
    """Disable GPU usage for all tests by default."""
    with patch("torch.cuda.is_available", return_value=False):
        yield


@pytest.fixture(autouse=True)
def disable_model_loading():
    """Disable model loading for all tests by default.

    This patches the create_model_dict function in the marker module to return a mock model dictionary,
    preventing the actual model loading which is slow and GPU-intensive.
    """
    # Create a comprehensive mock model dictionary with all required dependencies
    mock_model_dict = {
        "model": "mock_model",
        "texify_model": MagicMock(),
        "table_detector": MagicMock(),
        "table_structure_model": MagicMock(),
        "table_cell_model": MagicMock(),
        "ocr_model": MagicMock(),
        "layout_model": MagicMock(),
        "tokenizer": MagicMock(),
    }

    # Create mock rendered objects for PdfConverter and TableConverter
    mock_pdf_rendered = MagicMock()
    mock_pdf_rendered.metadata = {"title": "Mock Document"}

    mock_table_rendered = MagicMock()

    # Create patches for all GPU-dependent operations
    with patch(
        "cneutral_doc.document.parser.marker.create_model_dict"
    ) as mock_create_model_dict, patch(
        "cneutral_doc.document.parser.marker.PdfConverter"
    ) as mock_pdf_converter, patch(
        "cneutral_doc.document.parser.marker.TableConverter"
    ) as mock_table_converter:

        # Set up the mocks
        mock_create_model_dict.return_value = mock_model_dict

        # Mock the PdfConverter and TableConverter to return mock rendered objects
        mock_pdf_converter.return_value.return_value = mock_pdf_rendered
        mock_table_converter.return_value.return_value = mock_table_rendered

        yield


@pytest.fixture
def mock_parsed_document():
    """Create a mock parsed document for testing."""
    return ParsedDocument(
        name="test_document",
        pages=[
            Page(
                content="Test page content", page_number=1, metadata={"source": "test"}
            ),
            Page(content="Another test page", page_number=2, metadata={}),
        ],
        metadata={"title": "Test Document", "pages": 2},
        raw_data="Raw test data",
        tables=[Table(content="Test table content", index=0, page_number=1)],
    )


@pytest.fixture
def mock_pdf_file(tmp_path):
    """Create a mock PDF file for testing."""
    pdf_path = tmp_path / "test.pdf"
    pdf_path.write_bytes(b"%PDF-1.5\nMock PDF content")
    return pdf_path


# Mock responses for document parsing
MOCK_PARSED_TEXT = """{0}------------------------------------------------

Sample page content for testing.
This is a mock response that doesn't require actual parsing.

{1}------------------------------------------------

Second page content with a table:
| Header 1 | Header 2 |
| -------- | -------- |
| Data 1   | Data 2   |
"""

MOCK_TABLES = [
    "| Header 1 | Header 2 |\n| -------- | -------- |\n| Data 1   | Data 2   |"
]

MOCK_METADATA = {"title": "Mock Document", "pages": 2, "author": "Test Author"}


@pytest.fixture
def mock_marker_parser():
    """Create a fully mocked MarkerParser that doesn't use any real processing."""
    from cneutral_doc.document.parser.marker import MarkerParser

    parser = MarkerParser()

    # Mock the internal methods that would use GPU/resources
    parser._parse_text = MagicMock(return_value=(MOCK_PARSED_TEXT, [], MOCK_METADATA))
    parser._parse_tables = MagicMock(return_value=MOCK_TABLES[0])

    # Return the mocked parser
    return parser


class AsyncMock(MagicMock):
    """Helper class for mocking async methods."""

    async def __call__(self, *args, **kwargs):
        return super(AsyncMock, self).__call__(*args, **kwargs)
